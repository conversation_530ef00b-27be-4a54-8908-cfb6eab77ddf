# Build stage
FROM node:20-alpine AS build

# Set working directory
WORKDIR /app

# Copy package files
COPY package*.json ./

# Install dependencies
RUN npm install

# Copy all files
COPY . .

# Development stage
FROM build AS development
EXPOSE 5173
ENV NODE_ENV=development
ENV VITE_HOST=0.0.0.0
CMD ["npm", "run", "dev", "--", "--host", "0.0.0.0"]

# Production build stage
FROM build AS production
ENV NODE_ENV=production
RUN npm run build

# Production serve stage
FROM nginx:alpine AS serve
COPY --from=production /app/dist /usr/share/nginx/html
COPY nginx.conf /etc/nginx/conf.d/default.conf
EXPOSE 80
CMD ["nginx", "-g", "daemon off;"] 