version: '3.8'

services:
  # Development service
  app-dev:
    build:
      context: .
      target: development
    container_name: nepal-app-dev
    volumes:
      - .:/app
      - /app/node_modules
    ports:
      - "5173:5173"
    environment:
      - NODE_ENV=development
      - VITE_HOST=0.0.0.0
    command: npm run dev -- --host 0.0.0.0
    networks:
      - nepal-network

  # Production service
  app-prod:
    build:
      context: .
      target: serve
    container_name: nepal-app-prod
    ports:
      - "80:80"
    environment:
      - NODE_ENV=production
    networks:
      - nepal-network
    depends_on:
      - app-dev

networks:
  nepal-network:
    driver: bridge 