import { StrictMode } from 'react';
import { createRoot } from 'react-dom/client';
import './index.css';
import App from './App.tsx';
import { BrowserRouter } from 'react-router-dom';
import { AuthProvider } from './context/AuthContext.tsx';
import './i18n/config.ts';
import { LanguageProvider } from './context/LanguageContext.tsx';
import { LocationProvider } from './context/LocationContext.tsx';
import { ProfileProvider } from './context/ProfileContext.tsx';
import { LoaderProvider } from './context/LoaderContext.tsx';

createRoot(document.getElementById('root')!).render(
  <StrictMode>
    <BrowserRouter>
      <LanguageProvider>
          <LoaderProvider>
            <AuthProvider>
              <ProfileProvider>
                <LocationProvider>
                  <App />
                </LocationProvider>
              </ProfileProvider>
            </AuthProvider>
          </LoaderProvider>
      </LanguageProvider>
    </BrowserRouter>
  </StrictMode>,
)
