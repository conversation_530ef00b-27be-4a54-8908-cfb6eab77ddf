{"common": {"signIn": "साइन इन", "signOut": "साइन आउट", "username": "प्रयोगकर्ता नाम", "password": "पासवर्ड", "rememberMe": "मलाई सम्झनुहोस्", "forgotPassword": "पासवर्ड बिर्सनुभयो?", "showPassword": "पासवर्ड देखाउनुहोस्", "hidePassword": "पासवर्ड लुकाउनुहोस्", "submit": "पेश गर्नुहोस्", "cancel": "रद्द गर्नुहोस्", "loading": "लोड हुँदैछ...", "error": "त्रुटि", "success": "सफल", "viewList": "सूची हेर्नुहोस्", "noDataAvailable": "कुनै डाटा उपलब्ध छैन", "reloadCaptcha": "क्याप्चा पुनः लोड गर्नुहोस्", "appTitle": "नेपाल इम्युनाइजेसन ड्यासबोर्ड", "skipToMain": "मुख्य भागमा जानुहोस्", "show": "देखाउनुहोस्", "entries": "प्रविष्टिहरू", "find": "खोज्नुहोस्:", "previous": "अघिल्लो", "next": "अर्को", "showing": "{{from}} देखि {{to}} सम्म, कुल {{total}} प्रविष्टिहरू देखाउँदै", "category": "क्याटेगोरी", "filter": "फिल्टर", "programData": "प्रोग्राम डाटा", "selectCategory": "-- क्याटेगोरी चयन गर्नुहोस् --", "selectFilter": "-- फिल्टर चयन गर्नुहोस् --", "categoryOptions": {"HR": "मानव स्रोत", "Facility": "सुविधा", "Geography": "भौगोलिक क्षेत्र"}, "filterOptions": {"Doctor": "डाक्टर", "Nurse": "नर्स", "CHW": "सिएचडब्ल्यु", "Basic": "बेसिक", "Primary": "प्राथमिक", "Secondary": "माध<PERSON>यम<PERSON>क", "Province": "प्रदेश", "District": "जिल्ला", "Palika": "पालिका"}, "province": "प्रदेश", "district": "जिल्ला", "municipality": "पालिका", "ward": "वडा", "selectProvince": "-- प्रदेश छान्नुहोस् --", "selectDistrict": "-- जिल्ला छान्नुहोस् --", "selectPalika": "-- पालिका छान्नुहोस् --", "selectWard": "-- वडा छान्नुहोस् --", "changePassword": "पासवर्ड परिवर्तन गर्नुहोस्", "signingOut": "लगआउट हुँदै...", "logout": "लगआउट", "viewReport": "{{title}} को HR रिपोर्ट हेर्नुहोस्"}, "auth": {"signInTitle": "साइन इन", "signInSubtitle": "ड्यासबोर्डमा पहुँच प्राप्त गर्न आफ्नो प्रयोगकर्ता नाम र पासवर्ड प्रविष्ट गर्नुहोस्", "verificationCode": "सत्यापन कोड", "enterVerificationCode": "सत्यापन कोड प्रविष्ट गर्नुहोस्", "invalidCredentials": "अमान्य प्रयोगकर्ता नाम वा पासवर्ड", "invalidVerificationCode": "अमान्य सत्यापन कोड", "sessionExpired": "तपाईंको सेसन समाप्त भएको छ। कृपया पुनः साइन इन गर्नुहोस्।", "passwordReset": "पासवर्ड रिसेट", "passwordResetSuccess": "पासवर्ड रिसेट निर्देशनहरू तपाईंको इमेलमा पठाइएको छ", "changePassword": "पासवर्ड परिवर्तन गर्नुहोस्", "currentPassword": "वर्तमान पासवर्ड", "newPassword": "नयाँ पासवर्ड", "confirmPassword": "पासवर्ड पुष्टि गर्नुहोस्", "passwordMismatch": "पासवर्डहरू मिल्दैनन्", "secureAccess": "नेपाल ड्यासबोर्डमा सुरक्षित पहुँच", "loginPageTitle": "नेपाल इम्युनाइजेसन | साइन इन", "captchaSpoken": "तपाईंको कोड लेख्नुहोस् {{code}}"}, "dashboard": {"pageTitle": "नेपाल इम्युनाइजेसन ड्यासबोर्ड", "title": "ड्यासबोर्ड", "welcome": "नेपाल ड्यासबोर्डमा स्वागत छ", "totalFacilities": "कुल सुविधाहरू", "activeUsers": "सक्रिय प्रयोगकर्ताहरू", "recentActivities": "हालको गतिविधिहरू", "viewAll": "सबै हेर्नुहोस्", "geographyData": "भौगोलिक डाटा", "provinces": "प्रदेशहरू", "districts": "जिल्लाहरू", "palikas": "पालिकाहरू", "wards": "वडाहरू", "facilityData": "सुविधा डाटा", "geographyWiseList": "भौगोलिक अनुसारको सूची", "districtProvince": "प्रदेश", "total": "कुल", "basic": "बेसिक", "primary": "प्राथमिक", "secondary": "माध<PERSON>यम<PERSON>क", "tertiary": "तृतीयक", "private": "निजी", "ayurveda": "आयुर्वेद", "laboratory": "प्रयोगशाला", "mapData": "नक्सा डाटा", "hrData": "एचआर डाटा", "doctor": "डाक्टर", "nurse": "नर्स", "chw": "सिएचडब्ल्यु", "facilityTypes": {"basic": "बेसिक स्वास्थ्य केन्द्रहरू", "primary": "प्राथमिक स्वास्थ्य केन्द्रहरू", "tertiary": "तृतीयक अस्पतालहरू", "laboratory": "प्रयोगशालाहरू", "ayurvedic": "आयुर्वेदिक क्लिनिकहरू"}}, "facility": {"title": "सुविधाहरू", "addNew": "नयाँ सुविधा थप्नुहोस्", "search": "सुविधाहरू खोज्नुहोस्", "name": "सुविधा नाम", "type": "सुविधा प्रकार", "status": "स्थिति", "location": "स्थान", "actions": "कार्यहरू", "category": "क्याटेगोरी", "filter": "फिल्टर", "entriesPerPage": "प्रति पृष्ठ प्रविष्टिहरू", "searchPlaceholder": "सुविधाहरू खोज्नुहोस्...", "report": "रिपोर्ट", "filterData": "डाटा फिल्टर गर्नुहोस्", "searchButton": "खोज्नुहोस्", "show": "देखाउनुहोस्", "entries": "प्रविष्टिहरू", "find": "खोज्नुहोस्:", "table": {"slNo": "क्र.सं.", "district": "जिल्ला", "palika": "पालिका", "ward": "वडा", "facilityName": "सुविधा नाम", "facilityCode": "सुविधा कोड"}, "pagination": {"showing": "देखाउँदै", "to": "सम्म", "of": "मध्ये", "entries": "प्रविष्टिहरू", "previous": "अघिल्लो", "next": "अर्को"}, "categories": {"geographyData": "भौगोलिक डाटा", "humanResourceData": "मानव संसाधन डाटा", "facilityType": "सुविधा प्रकार", "specializedUnits": "विशेष इकाईहरू"}, "filterTypes": {"province": "प्रदेश", "district": "जिल्ला", "palika": "पालिका", "ward": "वडा"}}, "errors": {"required": "{{field}} आवश्यक छ", "invalidFormat": "अमान्य ढाँचा", "serverError": "सर्भर त्रुटि भयो। कृपया पुनः प्रयास गर्नुहोस्।", "networkError": "नेटवर्क त्रुटि। कृपया आफ्नो जडान जाँच गर्नुहोस्।", "unauthorized": "अनधिकृत पहुँच"}, "navbar": {"title": "नेपाल इम्युनाइजेसन ड्यासबोर्ड", "changePassword": "पासवर्ड परिवर्तन गर्नुहोस्", "signOut": "साइन आउट", "userRole": "व्यक्ति नाम"}, "sidebar": {"dashboard": "ड्यासबोर्ड", "geoProfile": "भौगोलिक प्रोफाइल", "geography": "भूगोल", "facility": "सुविधा", "geographyTypes": {"province": "प्रदेश", "district": "जिल्ला", "palika": "पालिका", "ward": "वडा"}, "facilityTypes": {"basic": "बेसिक", "primary": "प्राथमिक", "secondary": "माध<PERSON>यम<PERSON>क", "tertiary": "तृतीयक", "private": "निजी", "ayurveda": "आयुर्वेद", "laboratory": "प्रयोगशाला"}, "hr": {"hr": "मानव संसाधन", "doctor": "डाक्टर", "nurse": "नर्स", "chw": "सिएचडब्ल्यु"}, "userManagement": "यूजर म्यानेजमेन्ट"}, "geoprofile": {"pageTitle": "नेपाल इम्युनाइजेसन | भौगोलिक प्रोफाइल", "profileView": "प्रोफाइल दृश्य:", "userProfile": "प्रयोगकर्ता प्रोफाइल", "countryLevelLogin": "देश स्तरको लगइन: {{country}}", "searchCriteria": "खोज मापदण्ड", "province": "प्रदेश", "district": "जिल्ला", "palika": "पालिका", "ward": "वडा", "populateData": "डाटा लोड गर्नुहोस्", "geography": {"title": "भूगोल", "district": "जिल्ला", "palika": "पालिका", "ward": "वडा", "populationNote": "*लक्ष्य जनसंख्या (आ.व. २०८०/८१)"}, "facility": {"title": "स्वास्थ्य संस्था", "basic": "आधारभूत", "primary": "प्राथमिक", "secondary": "माध<PERSON>यम<PERSON>क", "tertiary": "उच्च", "private": "निजी", "ayurveda": "आयुर्वेद", "laboratory": "प्रयोगशाला"}, "specializedUnits": {"title": "विशेष इकाइहरू", "fruD": "एफआरयू-डि", "fruF": "एफआरयू-एफ", "dpD": "डीपी-डि", "dpF": "डीपी-एफ", "nbsuD": "एनबीएसयू-डि", "nbsuF": "एनबीएसयू-एफ", "sncuD": "एसएनसीयू-डि", "sncuF": "एसएनसीयू-एफ", "hwcD": "एचडब्लुसी-डि", "hwcF": "एचडब्लुसी-एफ"}, "hr": {"title": "मानव संसाधन", "doctor": "चिकित्सक (सुविधामा)", "nursing": "नर्सिङ", "paramedical": "प्यारामेडिकल", "chw": "सिएचडब्ल्यु", "mpw": "एमपीडब्ल्यू", "asha": "आशा कार्यकर्ता", "cho": "सीएचओ", "administrative": "प्रशासनिक"}, "hmis": {"title": "एचएमआईएस", "period": "बैशाख ८० - चैत ८० (१६ डिसेम्बर २०२४ मा)", "ANC": "गर्भ जाँच (ANC)", "Deliveries": "प्रसव", "BIRTH": "जन्म", "NEW BORN": "नवजात", "Number of Infant Deaths (1 -12 months)": "शिशु मृत्यु (१–१२ महिना)", "Number of Infant Deaths (1 -5 years)": "शिशु मृत्यु (१–५ वर्ष)", "Number of Maternal Deaths": "मातृ मृत्यु", "Number of Infant deaths": "शिशु मृत्यु", "(SAM)": "(SAM)", "anc_total_registered": "ANC को लागि दर्ता भएका गर्भवतीहरूको कुल संख्या", "anc_registered_1st_trimester": "१२ हप्ताभित्र दर्ता भएका महिलाहरू", "anc_4_or_more_checkups": "चार वा बढी ANC चेकअप गराएका महिलाहरू", "home_deliveries_sba": "SBA द्वारा गरिएको घरमा प्रसव", "institutional_deliveries": "संस्थागत प्रसव (C-सेक्शन सहित)", "c_section_deliveries": "C-सेक्शन मार्फत गरिएका प्रसवहरू", "live_birth_male": "जिवित जन्म - पुरुष", "live_birth_female": "जिवित जन्म - महिला", "still_birth": "मृत जन्म", "newborn_underweight": "२.५ केजी भन्दा कम तौल भएका नवजातहरू", "newborn_6_hbnc": "ID पछि ६ पटक HBNC पाएका नवजातहरू", "newborn_breastfed_1hr": "जन्मपछिको १ घण्टाभित्र स्तनपान गराइएका नवजातहरू", "infant_death_pneumonia": "निमोनियाका कारण मृत्यु", "infant_death_diarrhoea": "झाडापखालाका कारण मृत्यु", "infant_death_fever": "ज्वरो सम्बन्धी कारण मृत्यु", "infant_death_measles": "लालपानिका कारण मृत्यु", "infant_death_others": "अन्य कारणले मृत्यु", "infant_5yr_pneumonia": "निमोनियाका कारण मृत्यु", "infant_5yr_diarrhoea": "झाडापखालाका कारण मृत्यु", "infant_5yr_fever": "ज्वरो सम्बन्धी कारण मृत्यु", "infant_5yr_measles": "लालपानिका कारण मृत्यु", "infant_5yr_others": "अन्य कारणले मृत्यु", "maternal_death_aph": "APH का कारण मृत्यु", "maternal_death_pph": "PPH का कारण मृत्यु", "maternal_death_fever": "उच्च ज्वरोका कारण मृत्यु", "maternal_death_abortion": "गर्भपतनका कारण मृत्यु", "maternal_death_labour": "लामो/अवरोधित प्रसवका कारण मृत्यु", "maternal_death_hypertension": "उच्च रक्तचाप/दौरा कारण मृत्यु", "maternal_death_others": "अन्य/अज्ञात कारणले मृत्यु", "infant_24hr": "जन्मपछिको २४ घण्टाभित्र मृत्यु", "infant_sepsis": "४ हप्ताभित्र सेप्सिसका कारण मृत्यु", "infant_asphyxia": "४ हप्ताभित्र सास फेर्न नसक्ने कारण मृत्यु", "infant_other_causes": "४ हप्ताभित्र अन्य कारणले मृत्यु", "Fully Immunized": "टीकाकरण सम्पन्न", "fully_immunized_male": "९-११ महिनाका बालकहरू - टीकाकरण सम्पन्न", "fully_immunized_female": "९-११ महिनाका बालिकाहरू - टीकाकरण सम्पन्न", "sam": "गम्भीर कुपोषण (SAM)"}, "ncd": {"title": "एनसीडी", "period": "नोभेम्बर २०२४", "enrolledOver30": "३० वर्षभन्दा माथिका दर्ता भएका", "screened": "स्क्रिनिङ गरिएको", "partiallyScreened": "आंशिक स्क्रिनिङ", "fullyScreened": "पूर्ण स्क्रिनिङ", "rescreened": "पुन: स्क्रिनिङ", "referredByScreening": "स्क्रिनिङबाट रिफर गरिएका", "diagnosed": "रोग पत्ता लागेको", "underTreatment": "उपचारमा रहेका", "followUpAdherence": "अनुगमनमा सहभागिता"}, "map": {"title": "नक्शा डाटा"}}, "categoryFilter": {"category": "क्याटेगोरी", "filter": "फिल्टर", "selectCategory": "-- क्याटेगोरी छान्नुहोस् --", "categoryDescription": "सम्बन्धित विकल्पहरू फिल्टर गर्न क्याटेगोरी चयन गर्नुहोस्।", "selectFilter": "-- फिल्टर छान्नुहोस् --", "categories": {"hr": "जन<PERSON>क्ति", "facility": "संस्था", "geography": "भूगोल"}, "filterDescription": "चयन गरिएको क्याटेगोरीमा आधारित फिल्टर चयन गर्नुहोस्।", "filters": {"doctor": "डाक्टर", "nurse": "नर्स", "chw": "सिएचडब्ल्यु", "basic": "बेसिक", "primary": "प्राथमिक", "secondary": "माध<PERSON>यम<PERSON>क", "tertiary": "तृतीयक", "private": "निजी", "ayurveda": "आयुर्वेद", "laboratory": "प्रयोगशाला", "province": "प्रदेश", "district": "जिल्ला", "palika": "पालिका"}}, "report": {"pageTitle": "नेपाल इम्युनाइजेसन | रिपोर्ट", "title": "रिपोर्ट", "toggleFilter": "फिल्टर देखाउनुहोस्/लुकाउनुहोस्", "filterData": "डेटा फिल्टर गर्नुहोस्", "userProfile": "प्रयोगकर्ता प्रोफाइल", "countryLogin": "देशस्तरको लगइन: नेपाल", "search": "खोज्नुहोस्", "selectCategoryFilter": "नतिजा हेर्नको लागि कृपया श्रेणी र फिल्टर चयन गर्नुहोस्।"}, "table": {"slNo": "क्रम संख्या", "province": "प्रदेश", "district": "जिल्ला", "palika": "पालिका", "ward": "वडा", "facilityName": "संस्थाको नाम", "facilityType": "संस्थाको प्रकार", "facilityCode": "संस्थाको कोड", "designation": "पद", "availability": "उपलब्धता"}, "filterBar": {"filters": {"basic": "बेसिक", "primary": "प्राथमिक", "secondary": "माध<PERSON>यम<PERSON>क", "tertiary": "तृतीयक", "private": "निजी", "ayurvedic": "आयुर्वेद", "laboratory": "प्रयोगशाला"}, "label": "स्वास्थ्य संस्था छनोटहरू"}, "notifications": {"pageTitle": "नेपाल इम्युनाइजेसन | सूचना", "VACCINE_STOCK_ALERT": "{{vaccine}} खोपको स्टक सीमा मुनि छ।", "UNKNOWN": "तपाईंलाई नयाँ सूचना छ।", "noNotifications": "कुनै सूचना उपलब्ध छैन।", "unread": "{{count}} वटा नपढिएको सूचना", "none": "नयाँ सूचना छैन", "VACCINE_DELIVERED": "{{location}} मा {{quantity}} खोप वितरण गरियो।", "IMMUNIZATION_COMPLETED": "{{location}} मा {{count}} व्यक्तिहरूलाई खोप दिइयो।", "STOCK_ALERT": "स्टक सूचना: {{location}} मा {{vaccine}} {{level}} छ।", "REMINDER": "स्मरण: {{location}} मा {{name}} को खोप लाग्न बाँकी छ।", "DATA_SYNC_COMPLETE": "{{location}} का लागि डेटा समिकरण सम्पन्न।", "NEW_REGISTRATION": "नयाँ दर्ता: {{name}}, उमेर {{age}}।", "IMMUNIZATION_CAMPAIGN_STARTED": "{{location}} मा {{vaccine}} को खोप अभियान सुरु भयो।", "VACCINE_WASTAGE_ALERT": "{{location}} मा {{quantity}} खोप बर्बाद भयो।", "SYSTEM_ALERT": "{{message}}", "NEW_IMMUNIZATION_RECORD": "{{name}} ले {{vaccine}} खोप प्राप्त गर्यो।"}, "See All": "सबै हेर्नुहोस्", "Notifications": "सूचनाहरू", "password": {"setTitle": "तपाईंको पासवर्ड सेट गर्नुहोस्", "new": "नयाँ पासवर्ड", "confirm": "पासवर्ड पुनः लेख्नुहोस्", "mismatch": "पासवर्डहरू मिलेनन्।", "setError": "पासवर्ड सेट गर्न असफल भयो। कृपया फेरि प्रयास गर्नुहोस्।", "success": "पासवर्ड सफलतापूर्वक सेट गरियो!", "passwordRequirements": "तपाईंको पासवर्ड कम्तीमा ८ अक्षरको हुनुपर्छ र ठूलो अक्षर, सानो अक्षर, अंक, तथा स्पेसियल क्यारेक्टर समावेश गर्नुपर्छ।", "minLength": "पासवर्ड कम्तिमा ८ अक्षर लामो हुनुपर्छ।", "uppercase": "पासवर्डमा कम्तिमा एउटा ठुलो अक्षर (A-Z) हुनुपर्छ।", "lowercase": "पासवर्डमा कम्तिमा एउटा सानो अक्षर (a-z) हुनुपर्छ।", "number": "पासवर्डमा कम्तिमा एउटा अंक (0-9) हुनुपर्छ।", "special": "पासवर्डमा कम्तिमा एउटा विशेष अक्षर (@, #, $, आदि) हुनुपर्छ।", "goToLogin": "लगइनमा जानुहोस्"}, "changePassword": {"title": "पासवर्ड परिवर्तन गर्नुहोस्", "currentPassword": "हालको पासवर्ड", "newPassword": "नयाँ पासवर्ड", "confirmPassword": "नयाँ पासवर्ड पुष्टि गर्नुहोस्", "passwordsDoNotMatch": "पासवर्डहरू मेल खाँदैनन्।", "submit": "पासवर्ड परिवर्तन गर्नुहोस्", "success": "पासवर्ड सफलतापूर्वक परिवर्तन गरियो!", "error": "पासवर्ड परिवर्तन गर्न असफल भयो। कृपया फेरि प्रयास गर्नुहोस्।"}, "username": {"noSpaces": "प्रयोगकर्ताको नाममा खाली ठाउँ राख्न हुँदैन।"}, "email": {"invalid": "इमेल ठिक छैन।"}, "form": {"submit": "पासवर्ड सेट गर्नुहोस्", "submitting": "सेट हुँदैछ..."}, "manageusers": {"title": "प्रयोगकर्ता व्यवस्थापन", "addUser": "प्रयोगकर्ता थप्नुहोस्", "searchPlaceholder": "प्रयोगकर्ताहरू खोज्नुहोस्", "firstName": "पहिलो नाम", "middleName": "बी<PERSON><PERSON>ो नाम", "lastName": "थर", "email": "इमेल", "username": "प्रयोगकर्ता नाम", "phone": "फोन/सम्पर्क", "role": "भूमिका", "selectRole": "भूमिका चयन गर्नुहोस्", "countryLevel": "देश स्तर", "provinceLevel": "प्रदेश स्तर", "districtLevel": "जिल्ला स्तर", "password": "पासवर्ड", "cancel": "रद्द गर्नुहोस्", "submit": "प्रयोगकर्ता थप्नुहोस्"}, "user": {"name": "नाम", "status": "स्टेटस", "createdOn": "बनाइएको मिति", "registrationTitle": "प्रयोगकर्ता दर्ता", "firstName": "पहिलो नाम", "firstNamePlaceholder": "पहिलो नाम लेख्नुहोस्", "middleName": "बी<PERSON><PERSON>ो नाम", "middleNamePlaceholder": "बीचको नाम लेख्नुहोस्", "lastName": "थर", "lastNamePlaceholder": "थर लेख्नुहोस्", "email": "इमेल", "emailPlaceholder": "इमेल लेख्नुहोस्", "username": "प्रयोगकर्ता नाम", "usernamePlaceholder": "प्रयोगकर्ता नाम छान्नुहोस्", "phone": "फोन/सम्पर्क", "phonePlaceholder": "फोन नम्बर लेख्नुहोस्", "listTitle": "प्रयोगकर्ताहरू", "userTitle": "प्रयोगकर्ता", "role": "भूमिका", "roleOptions": {"country": "देश स्तर", "province": "प्रदेश स्तर", "district": "जिल्ला स्तर"}, "editTitle": "एडिट", "updateButton": "अपडेट", "statusOptions": {"active": "एक्टिभ", "disabled": "डिसेबल्ड"}, "password": "पासवर्ड", "passwordPlaceholder": "पासवर्ड सेट गर्नुहोस्", "submitButton": "थप्नुहोस्"}, "vaccinationRecord": {"generalOverview": "खोप रेकर्ड – सामान्य अवलोकन", "totalVaccinesAdministered": "कुल खोप दिइएको संख्या", "doses": "डोजहरू", "vaccinesWasted": "खोप बर्बाद भएको", "estimatedDoses": "अनुमानित डोजहरू", "wastageRate": "खोप बर्बादी दर", "withinNationalThreshold": "राष्ट्रिय मापदण्ड भित्र", "fullyImmunizedChildren": "पूर्ण रूपमा खोप लगाएका बालबालिका", "age0to23Months": "(०–२३ महिना)", "mostAdministeredVaccine": "सबैभन्दा बढी दिइएको खोप", "leastAdministeredVaccine": "सबैभन्दा कम दिइएको खोप", "coldChainStatus": "कोल्ड चेन स्थिति", "noColdChainBreaches": "कोल्ड चेनमा कुनै समस्या रिपोर्ट भएको छैन", "reportingCompliance": "प्रतिवेदन पालना", "allMonthsSubmitted": "१००% (सबै महिना बुझाइएको)"}, "details": {"title": "विवरण"}, "authorityLevel": "प्राधिकरण स्तर", "authority": "प्राधिकरण", "ownership": "स्वामित्व", "local": "स्थानीय", "government": "सर<PERSON><PERSON><PERSON>", "localGovernment": "स्थानीय सरकार", "services": {"title": "सेवाहरू"}, "hrdetails": {"hrData": "एचआर डेटा", "total": "कुल", "totalHR": "कुल मानव संसाधन", "doctorInFacility": "सुविधामा डाक्टर", "nurse": "नर्स", "chw": "समुदाय स्वास्थ्य कार्यकर्ता"}, "token": {"invalidTitle": "अवैध वा म्याद समाप्त लिंक", "invalidMessage": "तपाईंले खोलेको लिंक अवैध, म्याद समाप्त, वा पहिले नै प्रयोग भइसकेको छ।"}}