{"common": {"signIn": "Sign In", "signOut": "Sign Out", "username": "Username", "password": "Password", "rememberMe": "Remember Me", "showPassword": "Show Password", "hidePassword": "Hide Password", "forgotPassword": "Forgot Password?", "submit": "Submit", "cancel": "Cancel", "loading": "Loading...", "error": "Error", "success": "Success", "viewList": "View List", "noDataAvailable": "No data available", "reloadCaptcha": "Reload captcha", "appTitle": "Nepal Immunization Dashboard", "skipToMain": "Skip to main content", "show": "Show", "entries": "entries", "find": "Find:", "previous": "Previous", "next": "Next", "showing": "Showing {{from}} to {{to}} of {{total}} entries", "category": "Category", "filter": "Filter", "programData": "Program Data", "hmisData": "HMIS Data", "selectCategory": "-- Select Category --", "selectFilter": "-- Select Filter --", "categoryOptions": {"HR": "HR", "Facility": "Facility", "Geography": "Geography"}, "filterOptions": {"Doctor": "Doctor", "Nurse": "Nurse", "CHW": "CHW", "Basic": "Basic", "Primary": "Primary", "Secondary": "Secondary", "Province": "Province", "District": "District", "Palika": "Palika"}, "province": "Province", "district": "District", "municipality": "Municipality", "ward": "Ward", "selectProvince": "-- Select Province --", "selectDistrict": "-- Select District --", "selectMunicipality": "-- Select Municipality --", "selectWard": "-- Select Ward --", "changePassword": "Change Password", "signingOut": "Signing out...", "viewReport": "View HR report for {{title}}"}, "auth": {"signInTitle": "Sign In", "signInSubtitle": "Enter your username and password to access the dashboard", "verificationCode": "Verification Code", "enterVerificationCode": "Enter Verification Code", "invalidCredentials": "Invalid username or password", "invalidVerificationCode": "Invalid verification code", "sessionExpired": "Your session has expired. Please sign in again.", "passwordReset": "Password Reset", "passwordResetSuccess": "Password reset instructions have been sent to your email", "changePassword": "Change Password", "currentPassword": "Current Password", "newPassword": "New Password", "confirmPassword": "Confirm Password", "passwordMismatch": "Passwords do not match", "secureAccess": "Secure access to Nepal Dashboard", "loginPageTitle": "Nepal Immunization | Login", "captchaSpoken": "CAPTCHA code is {{code}}"}, "dashboard": {"pageTitle": "Nepal Immunization Dashboard", "title": "Dashboard", "welcome": "Welcome to Nepal Dashboard", "totalFacilities": "Total Facilities", "activeUsers": "Active Users", "recentActivities": "Recent Activities", "viewAll": "View All", "geographyData": "GEOGRAPHY DATA", "provinces": "Provinces", "districts": "Districts", "palikas": "<PERSON><PERSON><PERSON>", "wards": "Wards", "facilityData": "FACILITY DATA", "geographyWiseList": "Geography Wise List", "districtProvince": "Province", "total": "Total", "basic": "Basic", "primary": "Primary", "secondary": "Secondary", "tertiary": "Tertiary", "private": "Private", "ayurveda": "Ayurveda", "laboratory": "Laboratory", "mapData": "MAP DATA", "hrData": "HR DATA", "doctor": "Doctor", "nurse": "Nurse", "chw": "CHW", "facilityTypes": {"basic": "Basic Health Centers", "primary": "Primary Health Centers", "tertiary": "Tertiary Hospitals", "laboratory": "Laboratories", "ayurvedic": "Ayurvedic Clinics"}}, "facility": {"title": "Facilities", "addNew": "Add New Facility", "search": "Search Facilities", "name": "Facility Name", "type": "Facility Type", "status": "Status", "location": "Location", "actions": "Actions", "category": "Category", "filter": "Filter", "entriesPerPage": "Entries per page", "searchPlaceholder": "Search facilities...", "report": "Report", "filterData": "Filter Data", "searchButton": "Search", "show": "Show", "entries": "entries", "find": "Find:", "table": {"slNo": "SL NO", "district": "District", "palika": "Palika", "ward": "Ward", "facilityName": "Facility Name", "facilityCode": "Facility Code"}, "pagination": {"showing": "Showing", "to": "to", "of": "of", "entries": "entries", "previous": "Previous", "next": "Next"}, "categories": {"geographyData": "Geography Data", "humanResourceData": "Human Resource Data", "facilityType": "Facility Type", "specializedUnits": "Specialized Units"}, "filterTypes": {"province": "Province", "district": "District", "palika": "Palika", "ward": "Ward"}}, "errors": {"required": "{{field}} is required", "invalidFormat": "Invalid format", "serverError": "Server error occurred. Please try again.", "networkError": "Network error. Please check your connection.", "unauthorized": "Unauthorized access"}, "navbar": {"title": "Nepal Immunization Dashboard", "changePassword": "Change Password", "signOut": "Sign out", "userRole": "Person Name"}, "sidebar": {"dashboard": "Dashboard", "geoProfile": "Geo Profile", "geography": "Geography", "facility": "Facility", "geographyTypes": {"province": "Province", "district": "District", "palika": "Palika", "ward": "Ward"}, "facilityTypes": {"basic": "Basic", "primary": "Primary", "secondary": "Secondary", "tertiary": "Tertiary", "private": "Private", "ayurveda": "Ayurveda", "laboratory": "Laboratory"}, "hr": {"hr": "HR", "doctor": "Doctor", "nurse": "Nurse", "chw": "CHW"}, "userManagement": "User Management"}, "geoprofile": {"pageTitle": "Nepal Immunization | Geoprofile", "title": "Geo Profile", "userProfile": "User Profile", "profileView": "Profile View", "searchCriteria": "Search Criteria", "countryLevelLogin": "Country Level Login: Nepal", "populateData": "Populate Data", "province": "Province", "district": "District", "palika": "Palika", "ward": "Ward", "geography": {"title": "Geography", "district": "District", "palika": "Palika", "ward": "Ward", "populationNote": "*Target Population (FY 2080-81)"}, "facility": {"title": "Facility", "basic": "Basic", "primary": "Primary", "secondary": "Secondary", "tertiary": "Tertiary", "private": "Private", "ayurveda": "Ayurveda", "laboratory": "Laboratory"}, "specializedUnits": {"title": "Specialized Units", "fruD": "FRU-D", "fruF": "FRU-F", "dpD": "DP-D", "dpF": "DP-F", "nbsuD": "NBSU-D", "nbsuF": "NBSU-F", "sncuD": "SNCU-D", "sncuF": "SNCU-F", "hwcD": "HWC-D", "hwcF": "HWC-F"}, "hr": {"title": "Human Resource", "doctor": "Doctor In Facility", "nursing": "Nursing", "paramedical": "Paramedical", "chw": "CHW", "mpw": "MPW", "asha": "ASHA", "cho": "CHO", "administrative": "Administrative"}, "hmis": {"title": "HMIS", "period": "Apr 23 - Mar 24 (as on 16th December 2024 12:24:51 PM)", "children_immunized_measles_rubella_12_23_months": "Immunization Program - Children Immunized - Measles/Rubella - 12-23 Months", "children_immunized_measles_rubella_9_11_months": "Immunization Program - Children Immunized - Measles/Rubella - 9-11 Months", "children_immunized_bcg_doses": "Immunization Program - Vaccine Type - Children Immunized - BCG Doses", "children_immunized_je": "Immunization Program - Vaccine Type - Children Immunized - JE", "children_immunized_td_pregnant_women_2": "Immunization Program - Vaccine Type - Children Immunized - TD (Pregnant Women) - 2", "children_immunized_dpt_hepb_hib_1st": "Immunization Program - Vaccine Type - Dose - Children Immunized - DPT-HepB-Hib 1st", "children_immunized_dpt_hepb_hib_2nd": "Immunization Program - Vaccine Type - Dose - Children Immunized - DPT-HepB-Hib 2nd", "children_immunized_dpt_hepb_hib_3rd": "Immunization Program - Vaccine Type - Dose - Children Immunized - DPT-HepB-Hib 3rd", "children_immunized_fipv_1st": "Immunization Program - Vaccine Type - Dose - Children Immunized - FIPV 1st", "children_immunized_fipv_2nd": "Immunization Program - Vaccine Type - Dose - Children Immunized - FIPV 2nd", "children_immunized_opv_1st": "Immunization Program - Vaccine Type - Dose - Children Immunized - OPV 1st", "children_immunized_opv_2nd": "Immunization Program - Vaccine Type - Dose - Children Immunized - OPV 2nd", "children_immunized_opv_3rd": "Immunization Program - Vaccine Type - Dose - Children Immunized - OPV 3rd", "children_immunized_pcv_1st": "Immunization Program - Vaccine Type - Dose - Children Immunized - PCV 1st", "children_immunized_pcv_2nd": "Immunization Program - Vaccine Type - Dose - Children Immunized - PCV 2nd", "children_immunized_pcv_3rd": "Immunization Program - Vaccine Type - Dose - Children Immunized - PCV 3rd", "children_immunized_rota_1st": "Immunization Program - Vaccine Type - Dose - Children Immunized - Rota 1st", "children_immunized_rota_2nd": "Immunization Program - Vaccine Type - Dose - Children Immunized - Rota 2nd", "fully_immunized_within_23_months": "Immunization Program - Fully Immunized - Within 23 months", "td_pregnant_women_1": "Immunization Program - TD - Pregnant Women - 1", "children_immunized_tcv": "Immunization Program - Vaccine - Children Immunized - TCV"}, "ncd": {"title": "NCD", "period": "Nov 2024", "enrolledOver30": "Enrolled Over 30", "screened": "Screened", "partiallyScreened": "Partially Screened", "fullyScreened": "Fully Screened", "rescreened": "Rescreened", "referredByScreening": "Referred By Screening", "diagnosed": "Diagnosed", "underTreatment": "Under Treatment", "followUpAdherence": "Follow-up Adherence"}, "map": {"title": "Map Data"}}, "categoryFilter": {"category": "Category", "filter": "Filter", "selectCategory": "-- Select Category --", "selectFilter": "-- Select Filter --", "categoryDescription": "Choose a category to filter related options.", "categories": {"hr": "HR", "facility": "Facility", "geography": "Geography"}, "filterDescription": "Choose a filter based on the selected category.", "filters": {"doctor": "Doctor", "nurse": "Nurse", "chw": "CHW", "basic": "Basic", "primary": "Primary", "secondary": "Secondary", "tertiary": "Tertiary", "private": "Private", "ayurveda": "Ayurvedic", "laboratory": "Laboratory", "province": "Province", "district": "District", "palika": "Palika"}}, "report": {"pageTitle": "Nepal Immunization | Report", "title": "Report", "toggleFilter": "Toggle Filter", "filterData": "Filter Data", "userProfile": "User Profile", "countryLogin": "Country Level Login: Nepal", "search": "Search", "selectCategoryFilter": "Please select a category and filter to see results."}, "table": {"slNo": "SL No", "province": "Province", "district": "District", "palika": "Palika", "ward": "Ward", "facilityName": "Facility Name", "facilityType": "Facility Type", "facilityCode": "Facility Code", "designation": "Designation", "availability": "Availability"}, "filterBar": {"filters": {"basic": "Basic", "primary": "Primary", "secondary": "Secondary", "private": "Private", "tertiary": "Tertiary", "ayurvedic": "Ayurvedic", "laboratory": "Laboratory"}, "label": "facility filters"}, "notifications": {"pageTitle": "Nepal Immunization | Notifications", "VACCINE_DELIVERED": "{{quantity}} vaccines delivered at {{location}}.", "IMMUNIZATION_COMPLETED": "{{count}} individuals immunized in {{location}}.", "STOCK_ALERT": "Stock alert: {{vaccine}} in {{location}} is {{level}}.", "REMINDER": "Reminder: {{name}} has an upcoming immunization in {{location}}.", "DATA_SYNC_COMPLETE": "Data sync completed for {{location}}.", "NEW_REGISTRATION": "New registration: {{name}}, age {{age}}.", "IMMUNIZATION_CAMPAIGN_STARTED": "Immunization campaign started in {{location}} for {{vaccine}}.", "VACCINE_WASTAGE_ALERT": "{{quantity}} vaccine doses wasted in {{location}}.", "SYSTEM_ALERT": "{{message}}", "NEW_IMMUNIZATION_RECORD": "{{name}} received {{vaccine}}.", "VACCINE_STOCK_ALERT": "{{vaccine}} vaccine stock is below threshold.", "UNKNOWN": "You have a new notification.", "noNotifications": "No notifications available.", "unread": "{{count}} unread notifications", "none": "No new notifications"}, "See All": "See All", "Notifications": "Notifications", "password": {"setTitle": "Set Your Password", "new": "New Password", "confirm": "Confirm Password", "mismatch": "Passwords don't match.", "setError": "Failed to set password. Please try again.", "title": "Change Password", "currentPassword": "Current Password", "newPassword": "New Password", "confirmPassword": "Confirm New Password", "submit": "Update Password", "passwordsDoNotMatch": "New passwords do not match.", "invalidCurrentPassword": "Current password is incorrect.", "unknownError": "Something went wrong. Please try again.", "success": "Password changed successfully. Please log in again with your new password.", "passwordRequirements": "Your password must be at least 8 characters long and include uppercase and lowercase letters, a number, and a special character", "minLength": "Password must be at least 8 characters long.", "uppercase": "Password must contain at least one uppercase letter.", "lowercase": "Password must contain at least one lowercase letter.", "number": "Password must contain at least one number.", "special": "Password must contain at least one special character.", "goToLogin": "Go to Login Page"}, "changePassword": {"title": "Change Password", "currentPassword": "Current Password", "newPassword": "New Password", "confirmPassword": "Confirm New Password", "passwordsDoNotMatch": "Passwords do not match.", "submit": "Change Password", "success": "Password changed successfully!", "error": "Failed to change password. Please try again."}, "username": {"noSpaces": "Username must not contain spaces."}, "email": {"invalid": "Email is not valid."}, "form": {"submit": "Set Password", "submitting": "Setting..."}, "manageusers": {"title": "Manage Users", "addUser": "Add User", "searchPlaceholder": "Search Users", "firstName": "First Name", "middleName": "Middle Name", "lastName": "Last Name", "email": "Email", "username": "Username", "phone": "Phone/Contact", "role": "Role", "selectRole": "Select Role", "countryLevel": "Country level", "provinceLevel": "Province level", "districtLevel": "District level", "password": "Password", "cancel": "Cancel", "submit": "Add User"}, "user": {"name": "Name", "status": "Status", "createdOn": "Created On", "registrationTitle": "User Registration", "firstName": "First Name", "firstNamePlaceholder": "Enter first name", "middleName": "Middle Name", "middleNamePlaceholder": "Enter middle name", "lastName": "Last Name", "lastNamePlaceholder": "Enter last name", "email": "Email", "emailPlaceholder": "Enter email", "username": "Username", "usernamePlaceholder": "Choose a username", "phone": "Phone/Contact", "phonePlaceholder": "Enter phone number", "listTitle": "Users", "userTitle": "User", "role": "Role", "roleOptions": {"center": "Country Level", "province": "Province Level", "district": "District Level", "municipality": "Municipality Level", "ward": "Ward Level"}, "statusOptions": {"active": "Active", "disabled": "Disabled"}, "editTitle": "Edit", "updateButton": "Update", "password": "Password", "passwordPlaceholder": "Set password", "submitButton": "Add"}, "vaccinationRecord": {"generalOverview": "Vaccination Record – General Overview", "totalVaccinesAdministered": "TOTAL VACCINES ADMINISTERED", "doses": "doses", "vaccinesWasted": "VACCINES WASTED", "estimatedDoses": "estimated doses", "wastageRate": "WASTAGE RATE", "withinNationalThreshold": "within national threshold", "fullyImmunizedChildren": "FULLY IMMUNIZED CHILDREN", "age0to23Months": "(0–23 months)", "mostAdministeredVaccine": "Most Administered Vaccine", "leastAdministeredVaccine": "Least Administered Vaccine", "coldChainStatus": "Cold Chain Status", "noColdChainBreaches": "No Cold Chain Breaches Reported", "reportingCompliance": "Reporting Compliance", "allMonthsSubmitted": "100% (All months submitted)"}, "details": {"title": "Authority Details"}, "authorityLevel": "Authority Level", "authority": "Authority", "ownership": "Ownership", "local": "Local", "government": "Government", "localGovernment": "Local Government", "contactEmail": "Email", "contactName": "Name", "contactMobile": "Mobile", "bedCount": "Bed Count", "functionalBedCount": "Functional Bed Count", "services": {"title": "Services"}, "hrdetails": {"hrData": "HR Data", "total": "Total", "totalHR": "Total HR", "doctorInFacility": "Doctor In Facility", "nurse": "Nurse", "chw": "CHW"}, "token": {"invalidTitle": "Invalid or Expired Link", "invalidMessage": "The link you followed is either invalid, expired, or has already been used."}}