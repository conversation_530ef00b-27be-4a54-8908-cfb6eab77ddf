import axiosInstance from "../axios";

// API Base URLs
const BASE_URL = '/location';
const COUNT_API = '/location/count';

export interface Ward {
  id: number;
  name: string;
  code: string;
  ward_number: number;
  is_active?: boolean;
  municipality_name?: string;
  district_name?: string;
  province_name?: string;
  municipality?: number;
  district?: number;
  province?: number;
}

export interface Municipality {
  id: number;
  name: string;
  code: string;
  municipality_type: string | number;
  municipality_type_name?: string;
  is_active?: boolean;
  district_name?: string;
  province_name?: string;
  district?: number;
  province?: number;
  wards?: Ward[];
}

export interface District {
  id: number;
  name: string;
  code: string;
  is_active?: boolean;
  province_name?: string;
  province?: number;
  municipalities?: Municipality[];
}

export interface Province {
  id: number;
  name: string;
  code: string;
  is_active?: boolean;
  country?: number;
  country_name?: string;
  districts?: District[];
}

export interface Country {
  id: number;
  name: string;
  code: string;
  provinces?: Province[];
}

export interface HierarchyResponse {
  countries: Country[];
}

export interface FlatLocationData {
  provinces: Province[];
  districts: District[];
  municipalities: Municipality[];
  wards: Ward[];
}

export interface LocationCounts {
  total_provinces: number;
  total_districts: number;
  total_municipalities: number;
  total_wards: number;
}

export interface PaginatedLocations<T> {
  type: string;
  items: T[];
  total_count: number;
  page_number: number;
  page_size: number;
  total_pages: number;
}

export interface ApiResponse<T> {
  status: 'success' | 'error';
  message: string;
  data: T;
}

// Filters
export interface Filters {
  province_id?: number | string;
  district_id?: number | string;
  municipality_id?: number | string;
  ward_id?: number | string;
  page_number?: number | string;
  page_size?: number | string;
}

// 1. Get hierarchical location data (nested)
export const getLocationHierarchy = async (): Promise<Country[]> => {
  const response = await axiosInstance.get<ApiResponse<HierarchyResponse>>(`${BASE_URL}/hierarchy`);
  if (response.data.status === 'success') {
    return response.data.data.countries;
  }
  throw new Error(response.data.message);
};

// 2. Get flat location data
export const getFlatLocationData = async (): Promise<any> => {
  const response = await axiosInstance.get<ApiResponse<any>>(`${BASE_URL}/flat`);
  if (response.data.status === 'success') {
    return response.data.data;
  }
  throw new Error(response.data.message);
};

// 3. Get location counts with optional filters
export const getLocationCounts = async (filters: Filters = {}): Promise<LocationCounts> => {
  const response = await axiosInstance.get<ApiResponse<LocationCounts>>(COUNT_API, { params: filters });
  if (response.data.status === 'success') {
    return response.data.data;
  }
  throw new Error(response.data.message);
};

// 4. Get filtered paginated locations by filter keys (province_id, district_id, municipality_id, ward_id)
export const getFilteredLocations = async <T = any>(
  filters: Filters = {}
): Promise<PaginatedLocations<T>> => {
  const response = await axiosInstance.get<ApiResponse<PaginatedLocations<T>>>(BASE_URL, { params: filters });
  if (response.data.status === 'success') {
    return response.data.data;
  }
  throw new Error(response.data.message);
};

// 5. Get list of countries
export const getCountries = async (): Promise<Country[]> => {
  const response = await axiosInstance.get<ApiResponse<Country[]>>(`${BASE_URL}/country`);
  if (response.data.status === 'success') {
    return response.data.data;
  }
  throw new Error(response.data.message);
};

// 6. Get country by id
export const getCountryById = async (id: number): Promise<Country> => {
  const response = await axiosInstance.get<ApiResponse<Country>>(`${BASE_URL}/country/${id}`);
  if (response.data.status === 'success') {
    return response.data.data;
  }
  throw new Error(response.data.message);
};

// Similarly for Provinces, Districts, Municipalities, Wards (list and by id)

export const getProvinces = async (params: Partial<Filters> = {}): Promise<Province[]> => {
  const response = await axiosInstance.get<ApiResponse<Province[]>>(`${BASE_URL}/province`, { params });
  if (response.data.status === 'success') {
    return response.data.data;
  }
  throw new Error(response.data.message);
};

export const getProvinceById = async (id: number): Promise<Province> => {
  const response = await axiosInstance.get<Province>(`${BASE_URL}/province/${id}`);
  return response.data;
};

export const getDistricts = async (params: Partial<Filters> = {}): Promise<District[]> => {
  const response = await axiosInstance.get<ApiResponse<District[]>>(`${BASE_URL}/district`, { params });
  if (response.data.status === 'success') {
    return response.data.data;
  }
  throw new Error(response.data.message);
};

export const getDistrictById = async (id: number): Promise<District> => {
  const response = await axiosInstance.get<District>(`${BASE_URL}/district/${id}`);
  return response.data;
};

export const getMunicipalities = async (params: Partial<Filters> = {}): Promise<Municipality[]> => {
  const response = await axiosInstance.get<ApiResponse<Municipality[]>>(`${BASE_URL}/municipality`, { params });
  if (response.data.status === 'success') {
    return response.data.data;
  }
  throw new Error(response.data.message);
};

export const getMunicipalityById = async (id: number): Promise<Municipality> => {
  const response = await axiosInstance.get<Municipality>(`${BASE_URL}/municipality/${id}`);
  return response.data;
};

export const getWards = async (params: Partial<Filters> = {}): Promise<Ward[]> => {
  const response = await axiosInstance.get<ApiResponse<Ward[]>>(`${BASE_URL}/ward`, { params });
  if (response.data.status === 'success') {
    return response.data.data;
  }
  throw new Error(response.data.message);
};

export const getWardById = async (id: number): Promise<Ward> => {
  const response = await axiosInstance.get<Ward>(`${BASE_URL}/ward/${id}`);
  return response.data;
};
