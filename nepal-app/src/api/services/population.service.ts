import axiosInstance from '../axios';

// Types for Population API responses
export interface PopulationSummaryResponse {
  status: string;
  message: string;
  data: {
    total_population: number;
    total_wards: number;
    total_expected_births: number;
    total_expected_pregnancies: number;
    data_year: number;
    age_groups: Array<{
      name: string;
      display_name: string;
      total_count: number;
    }>;
  };
}

export interface PopulationHierarchyResponse {
  status: string;
  message: string;
  data: Array<{
    location_type: string;
    location_id: string;
    location_name: string;
    location_code: string;
    total_population: number;
    total_wards: number;
    total_expected_births: number;
    total_expected_pregnancies: number;
    data_year: number;
    age_groups: Array<{
      name: string;
      display_name: string;
      total_count: number;
    }>;
    parent_province?: {
      id: string;
      name: string;
    };
    parent_district?: {
      id: string;
      name: string;
    };
    parent_municipality?: {
      id: string;
      name: string;
    };
    children: any[];
  }>;
}

export interface PopulationImportResponse {
  status: string;
  message: string;
  data: {
    import_id: number;
    total_records: number;
    processed_records: number;
    failed_records: number;
    results: {
      total_records: number;
      processed_records: number;
      failed_records: number;
      errors: string[];
    };
  };
}

// Filters for population data
export interface PopulationFilters {
  province_id?: number;
  district_id?: number;
  municipality_id?: number;
  ward_id?: number;
  data_year?: number;
}

export interface PopulationHierarchyFilters extends PopulationFilters {
  expand_level?: 'province' | 'district' | 'municipality' | 'ward';
}

// API Service Functions

/**
 * Get population summary with location filters and age group breakdowns
 */
export const getPopulationSummary = async (
  filters: PopulationFilters = {}
): Promise<PopulationSummaryResponse> => {
  const response = await axiosInstance.get<PopulationSummaryResponse>(
    '/population/data/summary/',
    { params: filters }
  );
  return response.data;
};

/**
 * Get hierarchical population data organized by location with expandable details
 */
export const getPopulationHierarchy = async (
  filters: PopulationHierarchyFilters = {}
): Promise<PopulationHierarchyResponse> => {
  const response = await axiosInstance.get<PopulationHierarchyResponse>(
    '/population/data/hierarchy/',
    { params: filters }
  );
  return response.data;
};

/**
 * Upload and import population data from CSV files
 */
export const uploadPopulationData = async (
  file: File,
  dataYear?: number
): Promise<PopulationImportResponse> => {
  const formData = new FormData();
  formData.append('file', file);
  if (dataYear) {
    formData.append('data_year', dataYear.toString());
  }

  const response = await axiosInstance.post<PopulationImportResponse>(
    '/population/imports/upload/',
    formData,
    {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    }
  );
  return response.data;
};

/**
 * Get population summary for a specific province
 */
export const getProvincePopulation = async (provinceId: number): Promise<PopulationSummaryResponse> => {
  return getPopulationSummary({ province_id: provinceId });
};

/**
 * Get population summary for a specific district
 */
export const getDistrictPopulation = async (districtId: number): Promise<PopulationSummaryResponse> => {
  return getPopulationSummary({ district_id: districtId });
};

/**
 * Get population summary for a specific municipality
 */
export const getMunicipalityPopulation = async (municipalityId: number): Promise<PopulationSummaryResponse> => {
  return getPopulationSummary({ municipality_id: municipalityId });
};

/**
 * Get population summary for a specific ward
 */
export const getWardPopulation = async (wardId: number): Promise<PopulationSummaryResponse> => {
  return getPopulationSummary({ ward_id: wardId });
};

/**
 * Get all provinces with their population data
 */
export const getAllProvincesPopulation = async (): Promise<PopulationHierarchyResponse> => {
  return getPopulationHierarchy({ expand_level: 'province' });
};

/**
 * Get all districts within a province with their population data
 */
export const getProvinceDistrictsPopulation = async (provinceId: number): Promise<PopulationHierarchyResponse> => {
  return getPopulationHierarchy({ 
    province_id: provinceId, 
    expand_level: 'district' 
  });
};

/**
 * Get all municipalities within a district with their population data
 */
export const getDistrictMunicipalitiesPopulation = async (districtId: number): Promise<PopulationHierarchyResponse> => {
  return getPopulationHierarchy({ 
    district_id: districtId, 
    expand_level: 'municipality' 
  });
};

/**
 * Get all wards within a municipality with their population data
 */
export const getMunicipalityWardsPopulation = async (municipalityId: number): Promise<PopulationHierarchyResponse> => {
  return getPopulationHierarchy({ 
    municipality_id: municipalityId, 
    expand_level: 'ward' 
  });
};

/**
 * Helper function to extract age group data by name
 */
export const getAgeGroupByName = (
  ageGroups: Array<{ name: string; display_name: string; total_count: number }>,
  name: string
): number => {
  const ageGroup = ageGroups.find(group => group.name === name);
  return ageGroup ? ageGroup.total_count : 0;
};

/**
 * Helper function to get specific age groups
 */
export const getSpecificAgeGroups = (
  ageGroups: Array<{ name: string; display_name: string; total_count: number }>
) => {
  return {
    pop0to11Months: getAgeGroupByName(ageGroups, 'Pop00to11Months'),
    pop12to23Months: getAgeGroupByName(ageGroups, 'Pop12to23Months'),
    pop0to23Months: getAgeGroupByName(ageGroups, 'Pop00to23Months'),
    pop0to35Months: getAgeGroupByName(ageGroups, 'Pop00to35Months'),
    pop0to59Months: getAgeGroupByName(ageGroups, 'Pop00to59Months'),
    pop0to14Years: getAgeGroupByName(ageGroups, 'Pop00to14Years'),
    femalePop15to44Years: getAgeGroupByName(ageGroups, 'FemalePop15to44Years'),
    malePop10to19Years: getAgeGroupByName(ageGroups, 'MalePop10to19Years'),
    femalePop10to19Years: getAgeGroupByName(ageGroups, 'FemalePop10to19Years'),
  };
};
