import axiosInstance from '../axios';

// --- Types ---
export interface User {
  id: number;
  fname: string;
  mname?: string;
  lname: string;
  fullName: string;
  email: string;
  username?: string;
  phone?: string;
  role: 'center' | 'province' | 'district' | 'municipality' | 'ward';
  status: 'active' | 'inactive' | 'disabled' | 'invited';
  createdOn: string;
  authorityLocationId?: number;
  organizationName?: string;
  positionName?: string;
  lastLogin?: string;
  positionId?: number;
  disable_reason?: string;
}

interface ApiUser {
  id: number;
  username: string;
  email: string;
  first_name: string;
  middle_name?: string;
  last_name: string;
  full_name?: string;
  authority_level: string;
  status: string;
  is_active: boolean;
  created_at: string;
  updated_at: string;
  authority_location_id?: number;
  organization_name?: string;
  position_id: number;
  position_name?: string;
  last_login?: string;
}

interface GetUsersResponse {
  status: string;
  message: string;
  data: {
    type: string;
    items: ApiUser[];
    total_count: number;
    page_number: number;
    page_size: number;
    total_pages: number;
    sort_by: string;
    sort_order: string;
  };
}

export interface GetUsersParams {
  page_number?: number;
  page_size?: number;
  search?: string;
  authority_level?: User['role'];
  status?: 'active' | 'inactive' | 'disabled' | 'invited';
  is_active?: boolean;
  organization?: number;
  position?: number;
  sort_by?: UserSortBy;
  sort_order?: 'asc' | 'desc';
}

type UserSortBy =
  | 'username'
  | 'first_name'
  | 'last_name'
  | 'email'
  | 'created_at'
  | 'last_login'
  | 'authority_level'
  | 'status'
  | 'is_active'
  | 'organization__name'
  | 'position__name';

export interface Position {
  id: number;
  name: string;
  description: string;
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

export interface Organization {
  id: number;
  name: string;
  description: string;
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

export interface ChangePasswordRequest {
  old_password: string;
  new_password: string;
  confirm_password: string;
}

export interface RequestPasswordReset {
  email: string;
}

export interface ConfirmPasswordResetRequest {
  token: string;
  new_password: string;
  confirm_password: string;
}

export interface AcceptPasswordInvitation {
  token: string;
  password: string;
  confirm_password: string;
}

export interface DisableReason {
  disable_reason: string
}

// --- Helper to map API user to frontend User ---
const mapApiUserToUser = (u: ApiUser): User => ({
  id: u.id,
  fname: u.first_name,
  mname: u.middle_name ?? '',
  lname: u.last_name,
  fullName: u.full_name || [u.first_name, u.middle_name, u.last_name].filter(Boolean).join(' '),
  email: u.email,
  username: u.username,
  role: (u.authority_level?.toLowerCase() || 'center') as User['role'],
  status: (u.status?.toLowerCase() || 'inactive') as User['status'],
  createdOn: u.created_at,
  authorityLocationId: u.authority_location_id,
  organizationName: u.organization_name,
  positionName: u.position_name,
  lastLogin: u.last_login,
});

// --- API Functions ---

export const getUsers = async (params?: GetUsersParams): Promise<{
  users: User[];
  total: number;
  page: number;
  pageSize: number;
  totalPages: number;
}> => {
  const config = {
    params,
    meta: {
      disableLoader: !!params?.search, // disable loader if search param exists
    },
  };

  const response = await axiosInstance.get<GetUsersResponse>('/user/', config);

  const { items, total_count, page_number, page_size, total_pages } = response.data.data;

  return {
    users: items.map(mapApiUserToUser),
    total: total_count,
    page: page_number,
    pageSize: page_size,
    totalPages: total_pages,
  };
};

export const getUserById = async (id: number): Promise<User> => {
  const response = await axiosInstance.get<ApiUser>(`/user/${id}`);
  return mapApiUserToUser(response.data);
};

export const createUser = async (data: Partial<User>): Promise<User> => {
  const response = await axiosInstance.post<ApiUser>('/user/', data);
  return mapApiUserToUser(response.data);
};

export const updateUser = async (id: number, data: Partial<User>): Promise<User> => {
  const response = await axiosInstance.put<ApiUser>(`/user/${id}`, data);
  return mapApiUserToUser(response.data);
};

export const patchUser = async (id: number, data: Partial<User>): Promise<User> => {
  const response = await axiosInstance.patch<ApiUser>(`/user/${id}/`, data);
  return mapApiUserToUser(response.data);
};

export const disableUser = async (id: number, data: Partial<User>): Promise<User> => {
  const response = await axiosInstance.post<ApiUser>(`/user/${id}/disable`, data);
  return mapApiUserToUser(response.data);
};

export const deleteUser = async (id: number): Promise<void> => {
  await axiosInstance.delete(`/user/${id}/`);
};

export const getPositions = async (): Promise<Position[]> => {
  const response = await axiosInstance.get(`/user/positions/`);
  return response.data.data.items;
};

export const getOrganizations = async (): Promise<Organization[]> => {
  const response = await axiosInstance.get(`/user/organizations/`);
  return response.data.data.items;
};

export const changePassword = async (data: ChangePasswordRequest): Promise<{ message: string }> => {
  const response = await axiosInstance.post('user/change_password', data);
  return response.data;
};

export const requestPasswordReset = async (data: RequestPasswordReset): Promise<{ message: string }> => {
  const response = await axiosInstance.post('user/request_password_reset/', data);
  return response.data;
};

export const confirmPasswordReset = async (data: ConfirmPasswordResetRequest): Promise<{ message: string }> => {
  const response = await axiosInstance.post('user/confirm_password_reset/', data);
  return response.data;
};

export const acceptPasswordInvitation = async (data: AcceptPasswordInvitation): Promise<{ message: string }> => {
  const response = await axiosInstance.post('user/accept_invitation', data);
  return response.data;
};