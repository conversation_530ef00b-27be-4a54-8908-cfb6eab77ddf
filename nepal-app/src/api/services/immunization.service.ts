import axiosInstance from "../axios";

type VaccineCenterFilters = {
  search?: string;
  province_id?: number;
  district_id?: number;
  municipality_id?: number;
  sort_by?: string;
  sort_order?: "asc" | "desc";
  page?: number;
  page_size?: number;
};

type VaccineCenter = {
  id: number;
  name: string;
  country: number;
  country_name: string;
  province: number;
  province_name: string;
  district: number;
  district_name: string;
  municipality: number;
  municipality_name: string;
  latitude: number;
  longitude: number;
  created_at: string;
  updated_at: string;
};

type VaccineCenterListResponse = {
  count: number;
  next: string | null;
  previous: string | null;
  results: VaccineCenter[];
};

type UploadResponse = {
  detail: string;
  errors?: string[];
};

// Dataset response
type ImmunizationCategory = {
  label: string;
  value: number;
};

type ImmunizationProgram = {
  program: string;
  total: number;
  categories: ImmunizationCategory[];
};

type ImmunizationDatasetResponse = {
  reportDate: string;
  data: ImmunizationProgram[];
};

// API Calls

// 1. Get list of vaccine storage centers
export const getVaccineStorageCenters = async (
  filters: VaccineCenterFilters = {}
): Promise<VaccineCenterListResponse> => {
  const params = new URLSearchParams();

  Object.entries(filters).forEach(([key, value]) => {
    if (value !== undefined && value !== null) {
      params.append(key, String(value));
    }
  });

  const response = await axiosInstance.get<VaccineCenterListResponse>(
    `/immunization/vaccine-center/`,
    { params }
  );
  return response.data;
};

// 2. Upload vaccine storage centers
export const uploadVaccineStorageCenters = async (
  file: File
): Promise<UploadResponse> => {
  const formData = new FormData();
  formData.append("file", file);

  const response = await axiosInstance.post<UploadResponse>(
    `/immunization/vaccine-center/upload/`,
    formData,
    {
      headers: {
        "Content-Type": "multipart/form-data",
      },
    }
  );
  return response.data;
};

type ImmunizationDatasetFilters = {
  month?: string;
  facility_id?: number;
  province_id?: number;
  district_id?: number;
  municipality_id?: number;
  ward_id?: number;
};

// 3. Get immunization dataset
export const getImmunizationDataset = async (
  filters: { month?: string; facility_id?: number } = {}
): Promise<ImmunizationDatasetResponse> => {
  const params = new URLSearchParams();

  Object.entries(filters).forEach(([key, value]) => {
    if (value !== undefined && value !== null) {
      params.append(key, String(value));
    }
  });

  const response = await axiosInstance.get<ImmunizationDatasetResponse>(
    `/immunization/dataset`,
    { params }
  );

  return response.data;
};
