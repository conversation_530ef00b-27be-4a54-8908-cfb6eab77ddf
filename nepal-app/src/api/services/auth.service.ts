import axiosInstance from '../axios';

interface LoginResponse {
  refreshToken: string;
  accessToken: string;
  user: {
    id: number;
    username: string;
    full_name: string;
    role: string;
    email: string;
  };
  message: string;
}

export const login = async (username: string, password: string): Promise<{
  loginData: LoginResponse;
}> => {
  try {
    const { data: loginData } = await axiosInstance.post<LoginResponse>('/user/auth/login', {
      username,
      password,
    });

    // Save token
    localStorage.setItem('authToken', loginData.accessToken);

    // Set Authorization header for future requests
    axiosInstance.defaults.headers.common['Authorization'] = `Bearer ${loginData.accessToken}`;

    // Fetch user profile and flats in parallel
    // const [userProfileRes, flatsRes] = await Promise.all([
    //   axiosInstance.get('/user/profile'),
    //   axiosInstance.get('/location/flat'),
    // ]);

    return {
      loginData
    };
  } catch (error: any) {
    if (error.response?.data?.non_field_errors) {
      throw new Error(error.response.data.non_field_errors[0]);
    }
    throw new Error('Login failed. Please try again.');
  }
};


export const logout = async (): Promise<void> => {
  try {
    await axiosInstance.post('/user/auth/logout');
  } catch (error) {
    console.error('Logout failed:', error);
  } finally {
    localStorage.removeItem('authToken');
  }
};

export const getCurrentUser = async () => {
  try {
    const { data } = await axiosInstance.get('/user/profile');
    return {
      id: data.id,
      username: data.username,
      name: data.full_name,
      role: data.authority_level || 'user',
      authority_level: data.authority_level,
      email: data.email,
    };
  } catch (error) {
    throw new Error('Failed to fetch user profile');
  }
};

export const verifyToken = async (): Promise<boolean> => {
  const token = localStorage.getItem('authToken');
  if (!token) return false;

  try {
    await getCurrentUser();
    return true;
  } catch {
    return false;
  }
};

export const changePassword = async (oldPassword: string, newPassword: string, confirmPassword: string): Promise<{ message: string }> => {
  try {
    const { data } = await axiosInstance.post('/user/change_password', {
      old_password: oldPassword,
      new_password: newPassword,
      confirm_password: confirmPassword,
    });
    return data;
  } catch (error: any) {
    if (error.response?.data?.old_password) {
      throw new Error(error.response.data.old_password[0]);
    }
    if (error.response?.data?.new_password) {
      throw new Error(error.response.data.new_password[0]);
    }
    if (error.response?.data?.confirm_password) {
      throw new Error(error.response.data.confirm_password[0]);
    }
    if (error.response?.data?.non_field_errors) {
      throw new Error(error.response.data.non_field_errors[0]);
    }
    throw new Error('Password change failed. Please try again.');
  }
};
