import axiosInstance from "../axios";

// ========== TYPES ==========

export type FacilityFilters = {
  search?: string;
  facility_type_id?: number;
  authority_level?: number;
  province?: number;
  district?: number;
  municipality?: number;
  operational_status?: string;
  page?: number;
  page_size?: number;
};

export interface Facility {
    id: string; // UUID
    facility_id: number;
    name: string;
    hf_code: string;
    hmis_code: string;
    facility_type: {
      id: number;
      name: string;
      code: string;
    };
    province?: {
      id: number;
      name: string;
      code: string;
      country: number;
      country_name: string;
      is_active: boolean;
      created_at: string;
      updated_at: string;
    };
    district?: {
      id: number;
      name: string;
      code: string;
      province: number;
      province_name: string;
      country_name: string;
      is_active: boolean;
      created_at: string;
      updated_at: string;
    };
    municipality?: {
      id: number;
      name: string;
      code: string;
      district: number;
      district_name: string;
      province_name: string;
      municipality_type: number;
      municipality_type_name: string;
      is_active: boolean;
      created_at: string;
      updated_at: string;
    };
    ward?: {
      id: number;
      name: string;
      code: string;
      ward_number: number;
      municipality: number;
      municipality_name: string;
      district_name: string;
      province_name: string;
      is_active: boolean;
      created_at: string;
      updated_at: string;
    };
  
    // Coordinates
    latitude: string;
    longitude: string;
  
    // Service Flags
    fru_d: boolean | null;
    fru_f: boolean | null;
    dp_d: boolean | null;
    dp_f: boolean | null;
    hwc_d: boolean | null;
    hwc_f: boolean | null;
    nbsu_d: boolean | null;
    nbsu_f: boolean | null;
    sncu_d: boolean | null;
    sncu_f: boolean | null;
  
    // Authority and Ownership
    authority_level?: {
      id: number;
      name: string;
      code: string;
    };
    authority: string | null;
    ownership: {
      id: number;
      name: string;
      code: string;
    }
    email?: string | null,
    website?: string | null,
    telephone?: string | null,
    contact_person?: string | null,
    contact_person_mobile?: number | null,
    bed_count?: number | null;
    functional_bed_count?: number | null;
    // Metadata
    facility_level: string | null;
    is_active: boolean;
    created_at: string;
    updated_at: string;
    created_by: string | null;
    updated_by: string | null;
 
    parent_facility_type: string | null;
    parent_facility_nin: string | null;
  }  

export interface MappedFacility {
    Facility_ID: string;
    name?: string;
    Province_ID: number;
    District_ID: number;
    Palika_ID: number;
    Ward_ID: number;
    Facility_Type: number;
    Facility_Name: string;
    Facility_Code: string;
    latitude: number;
    longitude: number;
  }
  

export interface FacilityListResponse {
  count: number;
  results: Facility[];
}

export type UploadResponse = {
  message: string;
  total_records: number;
  imported: number;
  skipped: number;
  errors: Array<{ row: number; error: string }>;
};

export type FacilityType = {
  id: number;
  name: string;
  code: string;
  description: string | null;
  is_active: boolean;
  created_at: string;
  updated_at: string;
};

export type AuthorityLevel = {
  id: number;
  name: string;
  code: string;
  description: string;
};

export type FacilityLevel = {
  id: number;
  name: string;
  code: string;
};

// ========== UTILS ==========

const toQueryParams = (filters: Record<string, any>) => {
  const params = new URLSearchParams();
  Object.entries(filters).forEach(([key, value]) => {
    if (value !== undefined && value !== null) {
      params.append(key, String(value));
    }
  });
  return params;
};

// ========== FACILITIES ==========

export const getFacilities = async (
  filters: FacilityFilters = {}
): Promise<FacilityListResponse> => {
  const params = toQueryParams(filters);
  const response = await axiosInstance.get("/facility/", { params });
  return response.data;
};

export const getFacilityById = async (id: string): Promise<Facility> => {
  const response = await axiosInstance.get(`/facility/${id}/`);
  return response.data;
};

// NOTE: Only include create/update if API supports it individually
export const createFacility = async (
  data: Partial<Facility>
): Promise<Facility> => {
  const response = await axiosInstance.post("/facility/facility/", data);
  return response.data;
};

export const updateFacility = async (
  id: number,
  data: Partial<Facility>
): Promise<Facility> => {
  const response = await axiosInstance.put(`/facility/facility/${id}/`, data);
  return response.data;
};

export const patchFacility = async (
  id: number,
  data: Partial<Facility>
): Promise<Facility> => {
  const response = await axiosInstance.patch(`/facility/facility/${id}/`, data);
  return response.data;
};

export const deleteFacility = async (id: number): Promise<void> => {
  await axiosInstance.delete(`/facility/facility/${id}/`);
};

// File import: Excel, CSV
export const importFile = async (
  endpoint: string,
  file: File
): Promise<UploadResponse> => {
  const formData = new FormData();
  formData.append("file", file);

  const response = await axiosInstance.post<UploadResponse>(
    endpoint,
    formData,
    {
      headers: { "Content-Type": "multipart/form-data" },
    }
  );
  return response.data;
};

export const importNHFRData = (file: File) =>
  importFile("/facility/import-nhfr/", file);

// ========== FACILITY TYPES ==========

export const listFacilityTypes = async (): Promise<FacilityType[]> => {
  const response = await axiosInstance.get("facility/facility-type/");
  return response.data;
};

export const getFacilityTypeById = async (
  id: number
): Promise<FacilityType> => {
  const response = await axiosInstance.get(`facility/facility-type/${id}/`);
  return response.data;
};

export const createFacilityType = async (
  data: Omit<FacilityType, "id">
): Promise<FacilityType> => {
  const response = await axiosInstance.post("facility/facility-type/", data);
  return response.data;
};

export const updateFacilityType = async (
  id: number,
  data: Partial<Omit<FacilityType, "id">>
): Promise<FacilityType> => {
  const response = await axiosInstance.put(
    `facility/facility-type/${id}/`,
    data
  );
  return response.data;
};

export const deleteFacilityType = async (id: number): Promise<void> => {
  await axiosInstance.delete(`facility/facility-type/${id}/`);
};

// ========== AUTHORITY LEVELS ==========

export const listAuthorityLevels = async (): Promise<AuthorityLevel[]> => {
  const response = await axiosInstance.get("facility/authority-level/");
  return response.data;
};

export const getAuthorityLevelById = async (
  id: number
): Promise<AuthorityLevel> => {
  const response = await axiosInstance.get(`facility/authority-level/${id}/`);
  return response.data;
};

export const createAuthorityLevel = async (
  data: Omit<AuthorityLevel, "id">
): Promise<AuthorityLevel> => {
  const response = await axiosInstance.post("facility/authority-level/", data);
  return response.data;
};

export const updateAuthorityLevel = async (
  id: number,
  data: Partial<Omit<AuthorityLevel, "id">>
): Promise<AuthorityLevel> => {
  const response = await axiosInstance.put(
    `facility/authority-level/${id}/`,
    data
  );
  return response.data;
};

export const deleteAuthorityLevel = async (id: number): Promise<void> => {
  await axiosInstance.delete(`facility/authority-level/${id}/`);
};

// ========== FACILITY LEVELS ==========

export const listFacilityLevels = async (): Promise<FacilityLevel[]> => {
  const response = await axiosInstance.get("facility/facility-level/");
  return response.data;
};

export const getFacilityLevelById = async (
  id: number
): Promise<FacilityLevel> => {
  const response = await axiosInstance.get(`facility/facility-level/${id}/`);
  return response.data;
};

export const createFacilityLevel = async (
  data: Omit<FacilityLevel, "id">
): Promise<FacilityLevel> => {
  const response = await axiosInstance.post("facility/facility-level/", data);
  return response.data;
};

export const updateFacilityLevel = async (
  id: number,
  data: Partial<Omit<FacilityLevel, "id">>
): Promise<FacilityLevel> => {
  const response = await axiosInstance.put(
    `facility/facility-level/${id}/`,
    data
  );
  return response.data;
};

export const deleteFacilityLevel = async (id: number): Promise<void> => {
  await axiosInstance.delete(`facility/facility-level/${id}/`);
};
