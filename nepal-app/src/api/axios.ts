import axios from 'axios';
import type {
  AxiosInstance,
  AxiosResponse,
  AxiosError,
  InternalAxiosRequestConfig,
} from 'axios';
import { setGlobalLoading } from './loaderHookBridge';
import { getDecodedToken } from '../utils/token';

// Create axios instance
const axiosInstance: AxiosInstance = axios.create({
  baseURL: import.meta.env.VITE_API_BASE_URL,
  headers: {
    'Content-Type': 'application/json',
  },
  timeout: 30000,
});

// Track active requests for global loader
let activeRequests = 0;

// Request Interceptor
axiosInstance.interceptors.request.use(
  (config: InternalAxiosRequestConfig): InternalAxiosRequestConfig => {
    // Add auth token
    const token = localStorage.getItem('authToken');
    if (token && config.headers) {
      config.headers.Authorization = `Bearer ${token}`;
    }

    // Handle authority param injection for GET requests
    const decoded = getDecodedToken();
  const isGetRequest = config.method?.toLowerCase() === 'get';
    const meta = (config as any).meta || {};
    const skipAuthority = meta.skipAuthority === true;

    // if (isGetRequest && !skipAuthority) {
    //   const decoded = getDecodedToken();

    //   const originalParams = config.params && typeof config.params === 'object'
    //     ? { ...(config.params as Record<string, any>) }
    //     : {};

    //   if (
    //     decoded &&
    //     decoded.authority_level &&
    //     decoded.authority_location_id &&
    //     decoded.authority_level !== 'center'
    //   ) {
    //     const paramKey = `${decoded.authority_level}_id`;

    //     // ✅ Only add if not already present
    //     if (!(paramKey in originalParams)) {
    //       originalParams[paramKey] = decoded.authority_location_id;
    //     }
    //   }

    //   // ✅ Always reassign full params back
    //   config.params = originalParams;

    //   console.log('[Merged Params]:', config.params);

    //       }

    // Loader logic using meta.disableLoader
    if (!meta.disableLoader) {
      activeRequests++;
      setGlobalLoading(true);
    }

    return config;
  },
  (error: AxiosError) => {
    setGlobalLoading(false);
    return Promise.reject(error);
  }
);

// Response Interceptor
axiosInstance.interceptors.response.use(
  (response: AxiosResponse): AxiosResponse => {
    const meta = (response.config as any).meta || {};

    if (!meta.disableLoader) {
      activeRequests = Math.max(activeRequests - 1, 0);
      if (activeRequests === 0) {
        setGlobalLoading(false);
      }
    }

    return response;
  },
  (error: AxiosError) => {
    const meta = (error.config as any)?.meta || {};

    if (!meta.disableLoader) {
      activeRequests = Math.max(activeRequests - 1, 0);
      if (activeRequests === 0) {
        setGlobalLoading(false);
      }
    }

    if (error.response?.status === 401) {
      localStorage.removeItem('authToken');
    }

    return Promise.reject(error);
  }
);

export default axiosInstance;