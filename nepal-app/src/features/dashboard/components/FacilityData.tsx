import React from "react";
import { useTranslation } from "react-i18next";
import { Table } from "../../../components/common/Table";
import type { Column } from "../../../components/common/Table";
import DashboardSectionWrapper from "./DashboardSectionWrapper";
import PieChart from "../../../components/charts/PieChart";
import type { Filters, Level } from "../../../components/common/GeoFilter";

type GeographyStats = {
  id: string;
  district: string;
  total: number;
  basic: number;
  primary: number;
  secondary: number;
  tertiary: number;
  private: number;
  ayurveda?: number;
  laboratory?: number;
};

type FacilityDataProps = {
  filters: Filters;
  level: Level;
};

const data: GeographyStats[] = [
  { id: "1", district: "Koshi", total: 3016, basic: 1258, primary: 146, secondary: 7, tertiary: 35, private: 1497, ayurveda: 72, laboratory: 1 },
  { id: "2", district: "<PERSON>hesh", total: 1302, basic: 1027, primary: 24, secondary: 8, tertiary: 6, private: 163, ayurveda: 73, laboratory: 1 },
  { id: "3", district: "Bagmati", total: 3016, basic: 1258, primary: 146, secondary: 7, tertiary: 35, private: 1497, ayurveda: 72, laboratory: 1 },
  { id: "4", district: "Gandaki", total: 1302, basic: 1027, primary: 24, secondary: 8, tertiary: 6, private: 163, ayurveda: 73, laboratory: 1  },
  { id: "5", district: "Lumbini", total: 0, basic: 0, primary: 0, secondary: 0, tertiary: 0, private: 0, ayurveda: 0, laboratory: 0 },
  { id: "6", district: "Karnali", total: 0, basic: 0, primary: 0, secondary: 0, tertiary: 0, private: 0, ayurveda: 0, laboratory: 0 },
  { id: "7", district: "Sudurpaschim", total: 0, basic: 0, primary: 0, secondary: 0, tertiary: 0, private: 0, ayurveda: 0, laboratory: 0 },
];

const FacilityData: React.FC<FacilityDataProps> = ({ filters, level }) => {
  const { t } = useTranslation();

  const filteredData = data.filter((item) => {
    if (level === "province" && filters.province) {
      return item.id === String(filters.province);
    }
    return true;
  });

  const columns: Column<GeographyStats>[] = [
    {
      header: "No.",
      accessor: "id",
      render: (_value, _row, index) => (index + 1).toString(),
    },
    { header: t("dashboard.provinces"), accessor: "district", className: "font-medium" },
    { header: t("dashboard.total"), accessor: "total" },
    { header: t("dashboard.basic"), accessor: "basic" },
    { header: t("dashboard.primary"), accessor: "primary" },
    { header: t("dashboard.secondary"), accessor: "secondary" },
    { header: t("dashboard.tertiary"), accessor: "tertiary" },
    { header: t("dashboard.private"), accessor: "private" },
    { header: t("dashboard.ayurveda"), accessor: "ayurveda" },
    { header: t("dashboard.laboratory"), accessor: "laboratory" },
  ];

  return (
    <DashboardSectionWrapper title={t("dashboard.facilityData")}>
      <div className="flex flex-col md:flex-row gap-6 px-8 pt-8 overflow-hidden">
        <div className="w-full md:w-4/5 overflow-x-auto">
          <Table<GeographyStats>
            title={t("dashboard.geographyWiseList")}
            columns={columns}
            data={filteredData}
            getRowKey={(row) => row.id}
          />
        </div>

        <div className="w-full md:w-1/5 mt-20">
          <PieChart />
        </div>
      </div>
    </DashboardSectionWrapper>
  );
};

export default FacilityData;
