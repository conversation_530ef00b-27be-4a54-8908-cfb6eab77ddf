import React, { useEffect, useState } from "react";
import GeoMap from "../../../components/common/GeoMap";
import nepalOutline from "../../../assets/maps/nepal-geo.json";
import provinceGeoJson from "../../../assets/maps/nepal-provinces.geo.json";
import districtGeoJson from "../../../assets/maps/nepal-districts-geo.json";
import palikaGeoJson from "../../../assets/maps/nepal-palika-geo.json";
import { getFacilities } from "../../../api/services/facility.service";
import { useTranslation } from "react-i18next";
import DashboardSectionWrapper from "./DashboardSectionWrapper";
import type { Filters, Level } from "../../../components/common/GeoFilter";
import type { FacilityListResponse, MappedFacility } from "../../../api/services/facility.service";
import FacilityFilter from "../../../components/common/FacilityFilter";

interface FacilityFilters {
  province_id?: number;
  district_id?: number;
  municipality_id?: number;
  ward_id?: number;
  facility_type_id?: number;
  facility_level_id?: number;
  authority_level_id?: number;
}

interface DashboardMapProps {
  filters: Filters;
  level: Level;
}

const cleanFilters = (
  filters: Filters,
  facility_type_id?: number,
  facility_level_id?: number,
  authority_level_id?: number
): FacilityFilters => ({
  province_id: filters.province ?? undefined,
  district_id: filters.district ?? undefined,
  municipality_id: filters.municipality ?? undefined,
  ward_id: filters.ward ?? undefined,
  ...(facility_type_id ? { facility_type_id } : {}),
  ...(facility_level_id ? { facility_level_id } : {}),
  ...(authority_level_id ? { authority_level_id } : {}),
});

const DashboardMap: React.FC<DashboardMapProps> = ({ filters, level }) => {
  const { t } = useTranslation();
  const [facilities, setFacilities] = useState<MappedFacility[]>([]);
  const [loading, setLoading] = useState(false);

  // 👇 New states for dropdowns
  const [facilityType, setFacilityType] = useState<number | undefined>(undefined);
  const [facilityLevel, setFacilityLevel] = useState<number | undefined>(undefined);
  const [authorityLevel, setAuthorityLevel] = useState<number | undefined>(undefined);

  useEffect(() => {
    setLoading(true);
    const apiFilters = cleanFilters(filters, facilityType, facilityLevel, authorityLevel);

    getFacilities(apiFilters)
      .then((data: FacilityListResponse) => {
        const mapped = data.results.map((f) => ({
          Facility_ID: f.id,
          name: f.name,
          Province_ID: f.province?.id ?? 0,
          District_ID: f.district?.id ?? 0,
          Palika_ID: f.municipality?.id ?? 0,
          Ward_ID: f.ward?.id ?? 0,
          Facility_Type: f.facility_type?.id ?? 0,
          Facility_Name: f.name,
          Facility_Code: f.hf_code,
          latitude: Number(f.latitude),
          longitude: Number(f.longitude),
        })
        );

        setFacilities(mapped);
      })
      .catch((e) => {
        console.error("Failed to fetch facilities", e);
        setFacilities([]);
      })
      .finally(() => setLoading(false));
  }, [filters, facilityType, facilityLevel, authorityLevel]);

  const filteredFacilities = facilities.filter((f) => {
    if (level === "province" && filters.province && f.Province_ID !== filters.province) return false;
    if (level === "district" && filters.district && f.District_ID !== filters.district) return false;
    if (level === "municipality" && filters.municipality && f.Palika_ID !== filters.municipality) return false;
    return true;
  });

  const getGeoJsonOutline = () => {
    if (level === "province") return provinceGeoJson;
    if (level === "district") return districtGeoJson;
    if (level === "municipality") return palikaGeoJson;
    return nepalOutline;
  };

  const getGeoJsonDivision = () => {
    if (level === "province") return districtGeoJson;
    if (level === "district") return palikaGeoJson;
    if (level === "municipality") return palikaGeoJson;
    return provinceGeoJson;
  };

  const getAllowedRegionId = () => {
    if (level === "province") return filters.province;
    // if (level === "district") return filters.district;
    // if (level === "municipality") return filters.municipality;
    return null;
  };

  if (loading) {
    return (
      <DashboardSectionWrapper title={t("dashboard.mapData")}>
        <div className="w-full px-4 pt-4 text-center">{t("loading")}</div>
      </DashboardSectionWrapper>
    );
  }

  return (
    <DashboardSectionWrapper title={t("dashboard.mapData")}>
      <div className="w-full px-4 pt-4">
        <FacilityFilter
          facilityType={facilityType}
          setFacilityType={setFacilityType}
          facilityLevel={facilityLevel}
          setFacilityLevel={setFacilityLevel}
          authority={authorityLevel}
          setAuthority={setAuthorityLevel}
        />
        <GeoMap
          level={level}
          allowedRegionId={getAllowedRegionId()}
          geoJsonOutline={getGeoJsonOutline()}
          geoJsonDivision={getGeoJsonDivision()}
          facilities={filteredFacilities}
          filterType={facilityType}
          onRegionClick={(code) => console.log("Clicked region:", code)}
          onMarkerClick={(facility) => console.log("Clicked facility:", facility)}
        />
      </div>
    </DashboardSectionWrapper>
  );
};

export default DashboardMap;
