import React, { useEffect, useState } from 'react';
import { Building, Building2, MapPin, Home } from 'lucide-react';
import { useTranslation } from 'react-i18next';

import InfoCard from '../../../components/common/InfoCard';
import DashboardSectionWrapper from './DashboardSectionWrapper';

import type { Level } from '../../../components/common/GeoFilter';
import { getLocationCounts } from '../../../api/services/location.service';

interface GeographyInfoProps {
  filters: any;
  level: Level;
}

interface LocationCounts {
  total_provinces: number;
  total_districts: number;
  total_municipalities: number;
  total_wards: number;
}

const GeographyInfo: React.FC<GeographyInfoProps> = ({ filters }) => {
  const { t } = useTranslation();
  const [counts, setCounts] = useState<LocationCounts | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    const fetchCounts = async () => {
      if (!filters) return;
      setLoading(true);
      setError(null);
      try {
        const data = await getLocationCounts(filters);
        setCounts(data);
      } catch (err: any) {
        console.error('Failed to fetch location counts:', err);
        setError(err.message || 'Failed to fetch data');
      } finally {
        setLoading(false);
      }
    };

    fetchCounts();
  }, [JSON.stringify(filters)]);

  const cardData = [
    {
      title: t('dashboard.provinces'),
      filter: 'Province',
      count: counts?.total_provinces ?? 0,
      icon: <Building className="h-12 w-12 text-black-200" strokeWidth={1.5} aria-hidden="true" />,
      countColor: 'text-yellow-400',
      bottomTitleColor: 'text-yellow-500',
      bottomTitleBgColor: 'bg-yellow-100',
      onViewList: () => console.log('Viewing Provinces'),
    },
    {
      title: t('dashboard.districts'),
      filter: 'District',
      count: counts?.total_districts ?? 0,
      icon: <Building2 className="h-12 w-12 text-black-200" strokeWidth={1.5} aria-hidden="true" />,
      countColor: 'text-teal-500',
      bottomTitleColor: 'text-teal-500',
      bottomTitleBgColor: 'bg-teal-200',
      onViewList: () => console.log('Viewing Districts'),
    },
    {
      title: t('dashboard.palikas'),
      filter: 'Municipality',
      count: counts?.total_municipalities ?? 0,
      icon: <MapPin className="h-12 w-12 text-black-200" strokeWidth={1.5} aria-hidden="true" />,
      countColor: 'text-blue-500',
      bottomTitleColor: 'text-blue-500',
      bottomTitleBgColor: 'bg-blue-200',
      onViewList: () => console.log('Viewing Palikas'),
    },
    {
      title: t('dashboard.wards'),
      filter: 'Ward',
      count: counts?.total_wards ?? 0,
      icon: <Home className="h-12 w-12 text-black-200" strokeWidth={1.5} aria-hidden="true" />,
      countColor: 'text-red-500',
      bottomTitleColor: 'text-red-500',
      bottomTitleBgColor: 'bg-red-200',
      onViewList: () => console.log('Viewing Wards'),
    },
  ];

  return (
    <DashboardSectionWrapper title={t('dashboard.geographyData')}>
      {loading && <div className="text-center text-sm text-gray-500">{t('common.loading')}</div>}
      {error && <div className="text-center text-sm text-red-500">{error}</div>}
      {!loading && !error && (
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6 px-8 pt-8">
          {cardData.map((card, idx) => (
            <InfoCard key={idx} {...card} />
          ))}
        </div>
      )}
    </DashboardSectionWrapper>
  );
};

export default GeographyInfo;
