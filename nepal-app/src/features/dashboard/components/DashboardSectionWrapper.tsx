import React from 'react';

type DashboardSectionWrapperProps = {
  title: string;
  children: React.ReactNode;
};

const DashboardSectionWrapper = ({ title, children }: DashboardSectionWrapperProps) => {
  const id = `section-title-${title.toLowerCase().replace(/\s+/g, '-')}`;

  return (
    <section
      className="border-0 shadow-lg relative flex flex-col min-w-0 break-words bg-white rounded-2xl mb-6 pb-8"
      aria-labelledby={id}
    >
      <div className="p-8 shadow-md rounded-t-2xl rounded-b-none bg-white">
        <h2 id={id} className="uppercase text-base font-normal text-gray-900 tracking-widest">
          {title}
        </h2>
      </div>
      {children}
    </section>
  );
};

export default DashboardSectionWrapper;
