import { useTranslation } from "react-i18next";
import DashboardSectionWrapper from "./DashboardSectionWrapper";
import HmisDashboard from "../../geo-profile/HmisData";
import type { Filters, Level } from "../../../components/common/GeoFilter";

interface ProgramDataProps {
  selectedMonth?: string;
  filters?: Filters;
  level?: Level;
  facilityId?: string;
}

const ProgramData: React.FC<ProgramDataProps> = ({selectedMonth, filters, level, facilityId}) => {
  const { t } = useTranslation();

  return (
    <DashboardSectionWrapper title={t('common.programData')}>
      <h5 className="px-6 pt-6">{t('common.hmisData')}</h5>
      <HmisDashboard selectedMonth={selectedMonth} filters={filters} level={level} facilityId={facilityId} />
    </DashboardSectionWrapper>
  );
};

export default ProgramData;