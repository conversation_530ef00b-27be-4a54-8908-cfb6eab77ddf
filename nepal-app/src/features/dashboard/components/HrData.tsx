import React from 'react';
import { Building, Building2, MapPin, Home } from 'lucide-react';
import DoctorIcon from '../../../assets/icons/doctor.svg?react';
import NurseIcon from '../../../assets/icons/nurse.svg?react';
import ChwIcon from '../../../assets/icons/chw.svg?react';
import HrCard from '../../../components/common/HRCard';
import { useTranslation } from 'react-i18next';
import DashboardSectionWrapper from './DashboardSectionWrapper';

const HrData: React.FC = () => {
  const { t } = useTranslation();
  
  return (
    <>
      <DashboardSectionWrapper title={t('dashboard.hrData', 'HR Data')}>
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6 p-8">
        <HrCard
          title={t('dashboard.doctor','Doctor')}
          filter="Doctor"
          count={1234}
          icon={<DoctorIcon className="h-14 w-14 text-black-200" strokeWidth={1.5} />}
          countColor="text-red-400"
          className="border-4 border-red-200"
          innerClass="px-8 py-6 mb-4,"
        />
        <HrCard
          title={t('dashboard.nurse','Nurse')}
          filter="Nurse"
          count={2165}
          icon={<NurseIcon className="h-14 w-14 text-black-200" strokeWidth={1.5} />}
          countColor="text-purple-500"
          className="border-4 border-purple-200"
          innerClass="px-8 py-6 mb-4,"
        />
        <HrCard
          title={t('dashboard.chw','CHW')}
          filter="CHW"
          count={1965}
          icon={<ChwIcon className="h-14 w-14 text-black-200" strokeWidth={1.5} />}
          countColor="text-emerald-500"
          className="border-4 border-emerald-200"
          innerClass="px-8 py-6 mb-4,"
        />
        </div>
        </DashboardSectionWrapper>
    </>
  );
};

export default HrData;
