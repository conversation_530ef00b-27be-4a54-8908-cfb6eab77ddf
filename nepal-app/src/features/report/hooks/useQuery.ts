import { useSearchParams } from 'react-router-dom';

export function useQueryParams() {
  const [params, setParams] = useSearchParams();

  const setQueryParam = (key: string, value: string) => {
    const newParams = new URLSearchParams(params);
    newParams.set(key, value);
    setParams(newParams);
  };

  return {
    category: params.get('category') || '',
    filter: params.get('filter') || '',
    setCategory: (val: string) => setQueryParam('category', val),
    setFilter: (val: string) => setQueryParam('filter', val),
  };
}
