import React, { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router-dom';
import { PROVINCES } from '../../../constants/province';
import { DISTRICTS } from '../../../constants/districts';
import { palikas } from '../../../constants/palika';
import { wards } from '../../../constants/ward';
import { hr_data } from '../../../constants/hr';
import { PaginatedTable } from '../../../components/common/PaginatedTable';
import { EyeIcon } from 'lucide-react';
import type { Column } from '../../../components/common/Table';
import type { Level, Filters } from '../../../components/common/GeoFilter';
import { getFacilities } from '../../../api/services/facility.service';

type GeographyKey = 'province' | 'district' | 'palika';

interface DynamicReportTableProps {
  category?: string;
  filter?: string;
  geoFilters?: Filters;
  geoLevel?: Level;
  shouldPopulate: boolean;
}

const columnsByCategoryFilter: Record<string, string[]> = {
  'Geography-Province': ['SL No', 'Province'],
  'Geography-District': ['SL No', 'Province', 'District'],
  'Geography-Palika': ['SL No', 'Province', 'District', 'Palika'],
  'Geography-Ward': ['SL No', 'Province', 'District', 'Palika', 'Ward'],
  'Facility-Any': ['SL No', 'Province', 'District', 'Palika', 'Ward', 'Facility Name', 'Facility Type', 'Facility Code'],
  'HR-Any': ['SL No', 'Designation', 'Availability'],
};

function DynamicReportTable({
  category,
  filter,
  geoFilters,
  geoLevel,
  shouldPopulate,
}: DynamicReportTableProps) {
  const { t } = useTranslation();
  const navigate = useNavigate();
  const [facilityData, setFacilityData] = useState<any[]>([]);
  const [loading, setLoading] = useState(false);

  const handleViewDetails = (facility: any) => {
    navigate(`/facilities/${facility.id}`);
  };

  useEffect(() => {
    const fetchFacilities = async () => {
      if (category === 'Facility' && filter) {
        try {
          setLoading(true);
  
          const filtersPayload: any = {
            facility_type_id: Number(filter),
          };
  
          // Add geoFilters only when shouldPopulate is true
          if (shouldPopulate && geoFilters) {
            if (geoFilters.province) filtersPayload.province_id = geoFilters.province;
            if (geoFilters.district) filtersPayload.district_id = geoFilters.district;
            if (geoFilters.municipality) filtersPayload.municipality_id = geoFilters.municipality;
            if (geoFilters.ward) filtersPayload.ward_id = geoFilters.ward;
          }
  
          const data = await getFacilities(filtersPayload);
          setFacilityData(data.results || []);
        } catch (err) {
          console.error('Error fetching facilities:', err);
        } finally {
          setLoading(false);
        }
      }
    };
  
    fetchFacilities();
  }, [category, filter, geoFilters, shouldPopulate]);

  if (!category || !filter) {
    return <p>{t('report.selectCategoryFilter')}</p>;
  }

  const key = `Geography-${filter}`;
  let columnHeaders: string[] = [];

  if (category === 'Geography') {
    columnHeaders = columnsByCategoryFilter[key] || ['SL No'];
  } else if (category === 'Facility') {
    columnHeaders = columnsByCategoryFilter['Facility-Any'];
  } else if (category === 'HR') {
    columnHeaders = columnsByCategoryFilter['HR-Any'];
  }

  let data: any[] = [];

  if (category === 'Geography') {
    const geoKey = filter.toLowerCase() as GeographyKey;
    if (geoKey === 'province') {
      data = PROVINCES.map((p, i) => ({ slNo: i + 1, province: p.name }));
    } else if (geoKey === 'district') {
      data = DISTRICTS.map((d, i) => ({
        slNo: i + 1,
        province: PROVINCES.find(p => p.id === d.provinceId)?.name || '',
        district: d.name,
      }));
    } else if (geoKey === 'palika') {
      data = palikas.map((p, i) => {
        const district = DISTRICTS.find(d => d.id === p.District_ID);
        const province = PROVINCES.find(prov => prov.id === district?.provinceId);
        return {
          slNo: i + 1,
          province: province?.name || '',
          district: district?.name || '',
          palika: p.Palika_Name,
        };
      });
    }
  } else if (category === 'Facility') {
    data = facilityData.map((f, i) => ({
      id: f.id,
      slNo: i + 1,
      province: f.province?.name || '',
      district: f.district?.name || '',
      palika: f.municipality?.name || '',
      ward: f.ward?.name || '',
      facilityName: f.name,
      facilityType: f.facility_type?.name || '',
      facilityCode: f.hf_code,
    }));
  } else if (category === 'HR') {
    const filteredHr = hr_data.filter(f => f.type.toLowerCase() === filter.toLowerCase());
    data = filteredHr.map((h, i) => ({
      slNo: i + 1,
      designation: h.designation_name,
      availability: h.hr_availability,
    }));
  }

  const columns: Column<any>[] = columnHeaders.map((header) => {
    const accessor =
      header === 'SL No'
        ? 'slNo'
        : header
            .replace(/\s+/g, '')
            .replace(/^\w/, (c) => c.toLowerCase());

    return { header: t(`table.${accessor}`, header), accessor };
  });

  if (category === 'Facility') {
    columns.push({
      header: t('table.actions', 'Actions'),
      accessor: 'actions',
      render: (_value, row) => (
        <button
          onClick={() => handleViewDetails(row)}
          title={t('table.viewDetails', 'View Details')}
        >
          <EyeIcon className="w-4 h-4" />
        </button>
      ),
    });
  }

  return (
    <PaginatedTable
      columns={columns}
      data={data}
      className="rounded-none border-none shadow-none"
    />
  );
}

export default DynamicReportTable;
