// import React from 'react';
// import { useTranslation } from 'react-i18next';
// import { useLocation } from '../../../context/LocationContext';
// import { PaginatedTable } from '../../../components/common/PaginatedTable';
// import type { Column } from '../../../components/common/Table';
// import type { Filters, Level } from '../../../components/common/GeoFilter';

// interface GeographyTableProps {
//   geoLevel: Level;
//   filters: Filters;
// }

// const levelToColumns: Record<Level, string[]> = {
//   center: ['SL No', 'Country'],
//   province: ['SL No', 'Province', 'District'],
//   district: ['SL No', 'Province', 'District', 'Municipality'],
//   municipality: ['SL No', 'Province', 'District', 'Municipality', 'Ward'],
//   ward: ['SL No', 'Province', 'District', 'Municipality', 'Ward'],
// };

// const GeographyTable: React.FC<GeographyTableProps> = ({ geoLevel, filters }) => {
//   const { t } = useTranslation();
//   const { flatLocations } = useLocation();

//   if (!flatLocations.provinces.length) {
//     return <div>{t('loading')}</div>;
//   }

//   const getAccessor = (header: string) =>
//     header === 'SL No' ? 'slNo' : header.toLowerCase();

//   const columns: Column<any>[] = levelToColumns[geoLevel].map(header => ({
//     header: t(`table.${getAccessor(header)}`, header),
//     accessor: getAccessor(header),
//   }));

//   let slNo = 1;
//   let data: any[] = [];

//   // Helper to check if a filter ID matches or filter is not set
//   const matchesFilter = (value: number | undefined, filterValue?: number | null) =>
//     !filterValue || value === filterValue;

//   const { provinces, districts, municipalities, wards } = flatLocations;

//   if (geoLevel === 'province') {
//     const filteredDistricts = districts.filter(d =>
//       matchesFilter(d.province, filters.province)
//     );

//     filteredDistricts.forEach(district => {
//       const province = provinces.find(p => p.id === district.province);
//       if (province) {
//         data.push({
//           slNo: slNo++,
//           province: province.name,
//           district: district.name,
//         });
//       }
//     });
//   }

//   if (geoLevel === 'district') {
//     const filteredMunicipalities = municipalities.filter(m =>
//       matchesFilter(m.district, filters.district)
//     );

//     filteredMunicipalities.forEach(m => {
//       const district = districts.find(d => d.id === m.district);
//       const province = district && provinces.find(p => p.id === district.province);

//       if (district && province) {
//         data.push({
//           slNo: slNo++,
//           province: province.name,
//           district: district.name,
//           municipality: m.name,
//         });
//       }
//     });
//   }

//   if (geoLevel === 'municipality') {
//     const filteredWards = wards.filter(w =>
//       matchesFilter(w.municipality, filters.municipality)
//     );

//     filteredWards.forEach(w => {
//       const municipality = municipalities.find(m => m.id === w.municipality);
//       const district = municipality && districts.find(d => d.id === municipality.district);
//       const province = district && provinces.find(p => p.id === district.province);

//       if (municipality && district && province) {
//         data.push({
//           slNo: slNo++,
//           province: province.name,
//           district: district.name,
//           municipality: municipality.name,
//           ward: w.name,
//         });
//       }
//     });
//   }

//   if (geoLevel === 'ward') {
//     const filteredWards = wards.filter(w =>
//       matchesFilter(w.municipality, filters.municipality) &&
//       matchesFilter(w.id, filters.ward)
//     );

//     filteredWards.forEach(w => {
//       const municipality = municipalities.find(m => m.id === w.municipality);
//       const district = municipality && districts.find(d => d.id === municipality.district);
//       const province = district && provinces.find(p => p.id === district.province);

//       if (municipality && district && province) {
//         data.push({
//           slNo: slNo++,
//           province: province.name,
//           district: district.name,
//           municipality: municipality.name,
//           ward: w.name,
//         });
//       }
//     });
//   }

//   return (
//     <PaginatedTable
//       columns={columns}
//       data={data}
//       className="rounded-none border-none shadow-none"
//     />
//   );
// };

// export default GeographyTable;
