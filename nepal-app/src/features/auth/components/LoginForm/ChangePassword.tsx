import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router-dom';
import Input from '../../../../components/common/Input';
import Button from '../../../../components/common/Button';
import ErrorModal from '../../../../components/common/ErrorModal';
import { validatePassword } from '../../../../utils/validators';
import { changePassword } from '../../../../api/services/auth.service';
import { useAuth } from '../../../../context/AuthContext';

const ChangePassword: React.FC = () => {
  const { t } = useTranslation();
  const navigate = useNavigate();
  const { logout } = useAuth();

  const [currentPassword, setCurrentPassword] = useState('');
  const [newPassword, setNewPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const [passwordErrors, setPasswordErrors] = useState<string[]>([]);
  const [isSubmitting, setIsSubmitting] = useState(false);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    const validationResult = validatePassword(newPassword);

    if (!validationResult.isValid) {
      setPasswordErrors(validationResult.errors.map(err => t(err)));
      setError(null);
      return;
    }

    if (newPassword !== confirmPassword) {
      setError(t('changePassword.passwordsDoNotMatch'));
      setPasswordErrors([]);
      return;
    }

    setError(null);
    setPasswordErrors([]);
    setIsSubmitting(true);

    try {
      const response = await changePassword(currentPassword, newPassword, confirmPassword);
      setSuccess(response.message || t('changePassword.success'));
      
      // Clear form
      setCurrentPassword('');
      setNewPassword('');
      setConfirmPassword('');
      
      // Wait a moment to show success message, then logout and redirect
      setTimeout(async () => {
        try {
          await logout();
          navigate('/login');
        } catch (logoutError) {
          console.error('Logout error:', logoutError);
          // Force redirect even if logout fails
          localStorage.removeItem('authToken');
          navigate('/login');
        }
      }, 2000);
      
    } catch (apiError: any) {
      setError(apiError.message || t('changePassword.error'));
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <form
      className="max-w-[60vw] my-10 mx-auto p-6 border border-gray-300 rounded-lg bg-white shadow-md"
      onSubmit={handleSubmit}
    >
      <h2 className="text-center mb-6 text-2xl">{t('changePassword.title')}</h2>

      {success && (
        <div className="mb-4 p-4 bg-green-100 border border-green-400 text-green-700 rounded">
          {success}
        </div>
      )}

      <div className="mb-2">
        <Input
          id="currentPassword"
          type="password"
          label={t('changePassword.currentPassword')}
          value={currentPassword}
          onChange={(e) => setCurrentPassword(e.target.value)}
          describedBy="password-help"
          required
          disabled={isSubmitting}
        />
      </div>

      <div className="mb-2">
        <Input
          id="newPassword"
          type="password"
          label={t('changePassword.newPassword')}
          value={newPassword}
          describedBy="password-help"
          onChange={(e) => setNewPassword(e.target.value)}
          aria-invalid={passwordErrors.length > 0}
          required
          disabled={isSubmitting}
        />
        <p id="password-help" className="text-xs text-gray-500 mt-1">
          {t('password.passwordRequirements')}      
        </p>
        {passwordErrors.length > 0 && (
          <ul
            className="text-red-500 text-xs mt-1 list-disc pl-4"
            role="alert"
            aria-live="polite"
          >
            {passwordErrors.map((err, idx) => (
              <li key={idx}>{err}</li>
            ))}
          </ul>
        )}
      </div>

      <div className="mb-2">
        <Input
          id="confirmPassword"
          type="password"
          label={t('changePassword.confirmPassword')}
          value={confirmPassword}
          onChange={(e) => setConfirmPassword(e.target.value)}
          aria-invalid={!!error && passwordErrors.length === 0}
          describedBy="password-help"
          required
          disabled={isSubmitting}
        />
      </div>

      {error && !passwordErrors.length && (
        <ErrorModal
          isOpen={!!error}
          message={error}
          onOk={() => setError(null)}
        />
      )}

      <Button variant="primary" type="submit" disabled={isSubmitting}>
        {isSubmitting ? t('common.submitting') : t('changePassword.submit')}
      </Button>
      {success && (
  <div className="mt-4 text-green-600 text-sm text-center" role="status">
    {t('changePassword.success')}
  </div>
)}

    </form>
  );
};

export default ChangePassword;
