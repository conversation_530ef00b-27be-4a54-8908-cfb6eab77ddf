import React, { useState, useRef } from 'react';
import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router-dom';
import Input from '../../../../components/common/Input';
import Captcha from '../../../../components/common/Captcha';
import Button from '../../../../components/common/Button';
import ErrorModal from '../../../../components/common/ErrorModal';
import Loader from '../../../../components/common/Loader';
import { login } from '../../../../api/services/auth.service';

const LoginForm: React.FC<{
  onSubmit?: (username: string, password: string) => Promise<void>;
  isLoading?: boolean;
  onForgotPassword?: () => void;
}> = ({ onSubmit, isLoading = false, onForgotPassword }) => {
  const { t } = useTranslation();
  const navigate = useNavigate();
  const [username, setUsername] = useState('');
  const [password, setPassword] = useState('');
  const [captchaInput, setCaptchaInput] = useState('');
  const [generatedCaptcha, setGeneratedCaptcha] = useState('');
  const [rememberMe, setRememberMe] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [localLoading, setLocalLoading] = useState(false);
  const [touched, setTouched] = useState({
    username: false,
    password: false,
    captcha: false,
  });
  const captchaRef = useRef<{ refreshCaptcha: () => void }>(null);

  const handleBlur = (field: keyof typeof touched) => {
    setTouched((prev) => ({ ...prev, [field]: true }));
  };

  const validateForm = () => {
    if (!username) {
      setError(t('errors.required', { field: t('common.username') }));
      return false;
    }
    if (!password) {
      setError(t('errors.required', { field: t('common.password') }));
      return false;
    }
    if (captchaInput.toUpperCase() !== generatedCaptcha.toUpperCase()) {
      setError(t('auth.invalidVerificationCode'));
      return false;
    }
    return true;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setTouched({ username: true, password: true, captcha: true });
    setError(null);

    if (!validateForm()) return;

    setLocalLoading(true);
    try {
      // Use loginService instead of fetch
      const response = await login(username, password);
      const { accessToken } = response.loginData;

      // Store token with consistent key
      localStorage.setItem('authToken', accessToken);

      // Reset form
      setUsername('');
      setPassword('');
      setCaptchaInput('');
      setRememberMe(false);
      captchaRef.current?.refreshCaptcha();

      // Call onSubmit if provided (for AuthContext integration)
      if (onSubmit) {
        await onSubmit(username, password);
      }

      // Redirect to dashboard
      navigate('/dashboard');
    } catch (err) {
      const errorMessage =
        err instanceof Error
          ? err.message
          : t('errors.loginFailed', { status: 'Unknown' });
      setError(errorMessage);
      captchaRef.current?.refreshCaptcha();
    } finally {
      setLocalLoading(false);
    }
  };

  return (
    <div className="w-[45vw] bg-[#f5f7fb] box-border h-fit max-h-full flex items-center">
      <div className="w-full px-8 py-6">
        <div className="mb-8">
          <h2 className="text-2xl font-bold text-[#0e2238]" id="login-title">
            {t('auth.signInTitle')}
          </h2>
          <p className="text-sm text-[#95aac9]" id="login-description">
            {t('auth.signInSubtitle')}
          </p>
        </div>

        <ErrorModal
          isOpen={!!error}
          message={error || ''}
          onOk={() => setError(null)}
          onClose={() => setError(null)}
          aria-live="assertive"
        />

        <form
          onSubmit={handleSubmit}
          className="space-y-4"
          aria-labelledby="login-title"
          aria-describedby="login-description"
          aria-busy={localLoading || isLoading}
        >
          <fieldset>
            <legend className="sr-only">{t('auth.signInTitle')}</legend>
            <div className="flex gap-8 w-full">
              <div className="w-72">
                <Input
                  id="username"
                  label={t('common.username')}
                  type="text"
                  placeholder={t('common.username')}
                  value={username}
                  onChange={(e) => {
                    setUsername(e.target.value);
                    setError(null);
                  }}
                  onBlur={() => handleBlur('username')}
                  required
                  aria-required="true"
                  autoComplete="username"
                  aria-invalid={!username && touched.username}
                  aria-describedby="username-error"
                />
                {touched.username && !username && (
                  <p id="username-error" className="text-red-600 text-sm">
                    {t('errors.required', { field: t('common.username') })}
                  </p>
                )}
              </div>

              <div className="w-72">
                <Input
                  id="password"
                  label={t('common.password')}
                  type="password"
                  placeholder={t('common.password')}
                  value={password}
                  onChange={(e) => {
                    setPassword(e.target.value);
                    setError(null);
                  }}
                  onBlur={() => handleBlur('password')}
                  required
                  aria-required="true"
                  autoComplete="current-password"
                  aria-invalid={!password && touched.password}
                  aria-describedby="password-error"
                />
                {touched.password && !password && (
                  <p id="password-error" className="text-red-600 text-sm">
                    {t('errors.required', { field: t('common.password') })}
                  </p>
                )}
              </div>
            </div>

            <div className="flex justify-between items-center mt-6">
              <div className="flex gap-8">
                <div className="w-72">
                  <Input
                    id="captcha"
                    label={t('auth.verificationCode')}
                    type="text"
                    placeholder={t('auth.enterVerificationCode')}
                    value={captchaInput}
                    onChange={(e) => {
                      setCaptchaInput(e.target.value.toUpperCase());
                      setError(null);
                    }}
                    onBlur={() => handleBlur('captcha')}
                    required
                    aria-required="true"
                    aria-describedby="captcha-error"
                    aria-invalid={
                      captchaInput.toUpperCase() !== generatedCaptcha.toUpperCase() &&
                      touched.captcha
                    }
                  />
                  {touched.captcha &&
                    captchaInput.toUpperCase() !== generatedCaptcha.toUpperCase() && (
                      <p id="captcha-error" className="text-red-600 text-sm">
                        {t('auth.invalidVerificationCode')}
                      </p>
                    )}
                </div>

                <Captcha
                  ref={captchaRef}
                  onChange={setGeneratedCaptcha}
                  aria-label={t('auth.captchaImageAlt')}
                />
              </div>
            </div>
          </fieldset>

          <div className="flex items-center justify-between">
            <div className="flex gap-52">
              <label htmlFor="rememberMe" className="flex items-center space-x-2 cursor-pointer">
                <input
                  id="rememberMe"
                  type="checkbox"
                  checked={rememberMe}
                  onChange={(e) => setRememberMe(e.target.checked)}
                  className="accent-blue-500 h-4 w-4"
                />
                <span className="text-sm text-[#0e2238]">{t('common.rememberMe')}</span>
              </label>

              {onForgotPassword && (
                <button
                  type="button"
                  className="text-sm text-blue-600 underline hover:text-blue-700"
                  onClick={onForgotPassword}
                >
                  {t('auth.forgotPassword')}
                </button>
              )}
            </div>
          </div>

          <div className="w-1/4 mt-4">
            <Button
              type="submit"
              variant="primary"
              fullWidth
              disabled={localLoading || isLoading}
              aria-busy={localLoading || isLoading}
            >
              {t('common.signIn')}
            </Button>
            <Loader isLoading={localLoading || isLoading} />
          </div>
        </form>
      </div>
    </div>
  );
};

export default LoginForm;