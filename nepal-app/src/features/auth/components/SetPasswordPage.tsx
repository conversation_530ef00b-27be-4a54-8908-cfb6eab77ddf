import React, { useState } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import Input from '../../../components/common/Input';
import Button from '../../../components/common/Button';
import PublicTopbar from '../../../components/layout/PublicNavbar';
import Loader from '../../../components/common/Loader';
import { validatePassword, doPasswordsMatch } from '../../../utils/validators';
import { acceptPasswordInvitation } from '../../../api/services/user.service';
import StatusModal from '../../../components/common/StatusModal';

const SetPassword: React.FC = () => {
  const { t } = useTranslation();
  const { token } = useParams<{ token: string }>();
  const navigate = useNavigate();

  const [password, setPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [errors, setErrors] = useState<string[]>([]);
  const [success, setSuccess] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);

  const [modalOpen, setModalOpen] = useState(false);
  const [modalMessage, setModalMessage] = useState('');
  const [modalType, setModalType] = useState<'error' | 'success'>('error');

  const handleModalClose = () => {
    setModalOpen(false);
    setModalMessage('');
  };  

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);
    setErrors([]);
    setSuccess(false);
  
    const { isValid, errors: validationErrors } = validatePassword(password);
    const match = doPasswordsMatch(password, confirmPassword);
  
    const allErrors = [...validationErrors];
    if (!match) allErrors.push(t('password.mismatch'));
  
    if (allErrors.length > 0) {
      setErrors(allErrors);
      setIsSubmitting(false);
      return;
    }
  
    try {
      if (!token) {
        setErrors([t('password.invalidToken')]);
        return;
      }
  
      await acceptPasswordInvitation({
        token,
        password,
        confirm_password: confirmPassword,
      });
  
      setSuccess(true);
      localStorage.removeItem('authToken');
      navigate('/login');
    } catch (error: any) {
      const apiError =
        error?.response?.data?.token?.[0] ||
        t('password.setError');
    
      setModalMessage(apiError);
      setModalType('error');
      setModalOpen(true);
    }
     finally {
      setIsSubmitting(false);
    }
  };
  

  return (
    <div className="h-screen flex flex-col overflow-hidden">
      <PublicTopbar />
      <div className="flex flex-grow items-center justify-center bg-gray-100 px-4">
        <form
          onSubmit={handleSubmit}
          aria-busy={isSubmitting}
          aria-describedby={errors.length > 0 ? 'error-list' : undefined}
          className="bg-white shadow-md rounded px-8 pt-6 pb-8 w-full max-w-md"
        >
          <h1 className="text-2xl font-bold mb-2 text-center">
            {t('password.setTitle')}
          </h1>

          <p className="text-xs text-gray-500 mb-6">
            {t('password.passwordRequirements')}
          </p>

          <div className="mb-4">
            <label htmlFor="new-password" className="block text-sm font-medium text-gray-700 mb-1">
              {t('password.new')}
            </label>
            <Input
              id="new-password"
              type="password"
              className="w-full px-3 py-2 border rounded focus:outline-none focus:ring focus:ring-blue-300"
              value={password}
              onChange={(e) => setPassword(e.target.value)}
            />
          </div>

          <div className="mb-4">
            <label htmlFor="confirm-password" className="block text-sm font-medium text-gray-700 mb-1">
              {t('password.confirm')}
            </label>
            <Input
              id="confirm-password"
              type="password"
              className="w-full px-3 py-2 border rounded focus:outline-none focus:ring focus:ring-blue-300"
              value={confirmPassword}
              onChange={(e) => setConfirmPassword(e.target.value)}
            />
          </div>

          {errors.length > 0 && (
            <ul
              id="error-list"
              role="alert"
              aria-live="assertive"
              className="text-red-500 text-sm mb-4 list-disc list-inside"
            >
              {errors.map((err, idx) => (
                <li key={idx}>{err}</li>
              ))}
            </ul>
          )}

          <Button
            type="submit"
            aria-disabled={isSubmitting}
            className="w-full bg-blue-600 text-white font-bold py-2 px-4 rounded hover:bg-blue-700 disabled:opacity-50"
            disabled={isSubmitting}
          >
            {isSubmitting ? t('form.submitting') : t('form.submit')}
          </Button>
          <Loader isLoading={isSubmitting} />
        </form>
        <StatusModal
          isOpen={modalOpen}
          onClose={handleModalClose}
          onOk={handleModalClose}
          message={modalMessage}
          type={modalType}
        />
      </div>
    </div>
  );
};

export default SetPassword;
