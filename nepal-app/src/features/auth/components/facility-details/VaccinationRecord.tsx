import React from "react";
import { useTranslation } from "react-i18next";
import { Shield, Activity, User<PERSON><PERSON><PERSON>, Baby, <PERSON>, Calendar } from "lucide-react";

interface StatCard {
  icon: React.ReactNode;
  value: string | number;
  labelKey: string; // Translation key for label
  subLabelKey?: string; // Translation key for sub label (optional)
  colorBg: string;
  colorBorder: string;
  colorText: string;
  subLabelClassName?: string;
}

interface VaccineDetail {
  titleKey: string; // Translation key for title
  vaccineName: string;
  doses: number;
  colorBg: string;
  colorBorder: string;
  colorText: string;
}

interface StatusIndicator {
  icon: React.ReactNode;
  titleKey: string; // Translation key for title
  descriptionKey: string; // Translation key for description
  colorBg: string;
  colorBorder: string;
  colorText: string;
  statusIcon: React.ReactNode;
}

interface VaccinationRecordProps {
  period: string;
  stats: StatCard[];
  vaccineDetails: VaccineDetail[];
  statusIndicators: StatusIndicator[];
}

const VaccinationRecord: React.FC<VaccinationRecordProps> = ({
  period,
  stats,
  vaccineDetails,
  statusIndicators,
}) => {
  const { t } = useTranslation();

  return (
    <div className="p-6">
      <div className="flex items-center justify-between mb-6">
        <h2 className="text-lg font-bold text-gray-900">
          {t("vaccinationRecord.generalOverview", "Vaccination Record – General Overview")}
        </h2>
        <span className="text-sm text-gray-600 bg-gray-100 px-3 py-1 rounded-full">{period}</span>
      </div>

      {/* Top Stats Row */}
      <div className="grid grid-cols-4 gap-6 mb-6">
        {stats.map(({ icon, value, labelKey, subLabelKey, colorBg, colorBorder, colorText, subLabelClassName }, idx) => (
          <div
            key={idx}
            className={`${colorBg} p-6 rounded-lg text-center border ${colorBorder}`}
          >
            <div className="flex items-center justify-center mb-2">
              <div
                className={`${colorBg.replace("50", "100")} rounded-full flex items-center justify-center mr-2 w-8 h-8`}
              >
                {React.isValidElement(icon) &&
                React.cloneElement(icon as React.ReactElement<any, any>, {
                    className: colorText,
                    size: 16,
                })}
              </div>
              <span className={`text-3xl font-bold ${colorText}`}>{value}</span>
            </div>
            <p className="text-xs text-gray-600 uppercase tracking-wide font-medium">{t(labelKey)}</p>
            {subLabelKey && (
              <p className={`text-xs mt-1 ${subLabelClassName ? subLabelClassName : "text-gray-500"}`}>
                {t(subLabelKey)}
              </p>
            )}
          </div>
        ))}
      </div>

      {/* Vaccine Details Row */}
      <div className="grid grid-cols-2 gap-6 mb-6">
        {vaccineDetails.map(({ titleKey, vaccineName, doses, colorBg, colorBorder, colorText }, idx) => (
          <div
            key={idx}
            className={`${colorBg} p-4 rounded-lg border ${colorBorder}`}
          >
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600 mb-1">{t(titleKey)}</p>
                <p className={`text-xl font-bold ${colorText}`}>{vaccineName}</p>
                <p className="text-sm text-gray-500">{doses} {t("vaccinationRecord.doses", "doses")}</p>
              </div>
              <div
                className={`${colorBg.replace("50", "100")} rounded-full flex items-center justify-center w-12 h-12`}
              >
                <Shield className={colorText} size={20} />
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* Status Indicators */}
      <div className="grid grid-cols-2 gap-6">
        {statusIndicators.map(({ icon, titleKey, descriptionKey, colorBg, colorBorder, colorText, statusIcon }, idx) => (
          <div
            key={idx}
            className={`${colorBg} p-4 rounded-lg border ${colorBorder} flex items-center justify-between`}
          >
            <div className="flex items-center space-x-3">
              <div
                className={`${colorBg.replace("50", "100")} rounded-full flex items-center justify-center w-8 h-8`}
              >
                {React.isValidElement(icon) &&
                React.cloneElement(icon as React.ReactElement<any, any>, {
                    className: colorText,
                    size: 16,
                })}
              </div>
              <div>
                <p className="font-medium text-gray-800">{t(titleKey)}</p>
                <p className="text-sm text-gray-600">{t(descriptionKey)}</p>
              </div>
            </div>
            <div className={`${colorText} text-xl`}>{statusIcon}</div>
          </div>
        ))}
      </div>
    </div>
  );
};

export default VaccinationRecord;
