import { CircleCheck, XCircle, CircleMinus, ListIcon } from 'lucide-react';
import { useTranslation } from 'react-i18next';

interface ServicesSectionProps {
  facility: {
    fru_d: boolean | null;
    fru_f: boolean | null;
    dp_d: boolean | null;
    dp_f: boolean | null;
    hwc_d: boolean | null;
    hwc_f: boolean | null;
    nbsu_d: boolean | null;
    nbsu_f: boolean | null;
    sncu_d: boolean | null;
    sncu_f: boolean | null;
  };
}

const ServiceIcon = ({ available }: { available: boolean | null }) => {
  if (available === true) return <CircleCheck className="text-green-500 w-5 h-5" />;
  if (available === false) return <XCircle className="text-red-500 w-5 h-5" />;
  if (available === null) return <CircleMinus className="text-yellow-500 w-5 h-5" />;
  return <ListIcon className="text-gray-500 w-5 h-5" />;
};

export default function ServicesSection({ facility }: ServicesSectionProps) {
  const { t } = useTranslation();

  const dynamicServiceGroups = [
    [
      { name: 'FRU-D', available: facility.fru_d },
      { name: 'FRU-F', available: facility.fru_f },
    ],
    [
      { name: 'DP-D', available: facility.dp_d },
      { name: 'DP-F', available: facility.dp_f },
    ],
    [
      { name: 'NBSU-D', available: facility.nbsu_d },
      { name: 'NBSU-F', available: facility.nbsu_f },
    ],
    [
      { name: 'SNCU-D', available: facility.sncu_d },
      { name: 'SNCU-F', available: facility.sncu_f },
    ],
    [
      { name: 'HWC-D', available: facility.hwc_d },
      { name: 'HWC-F', available: facility.hwc_f },
    ],
  ];

  return (
    <div className="py-6">
      <h2 className="text-lg font-bold mb-2">{t('services.title')}</h2>

      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-5 gap-8 p-2">
        {dynamicServiceGroups.map((group, idx) => (
          <div
            key={idx}
            className="bg-white border border-gray-300 rounded-lg shadow-sm p-4 cursor-pointer hover:shadow-md transition"
          >
            <ul>
              {group.map((service, i) => (
                <li key={i} className="flex justify-between py-1 text-gray-800 text-sm">
                  <span>{service.name}</span>
                  <ServiceIcon available={service.available} />
                </li>
              ))}
            </ul>
          </div>
        ))}
      </div>
    </div>
  );
}
