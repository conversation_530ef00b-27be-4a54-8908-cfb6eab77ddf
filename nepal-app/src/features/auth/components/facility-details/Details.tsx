import React from 'react';
import { useTranslation } from 'react-i18next';

type Detail = {
    label1Key: string;
    value1Key: string | number;
    label2Key: string;
    value2Key: string | number;
    icon: React.ReactNode;
  };  

type BedDetailsProps = {
  bedDetails: Detail[];
};

const BedDetails: React.FC<BedDetailsProps> = ({ bedDetails }) => {
  const { t } = useTranslation();

  return (
    <section className="w-full mt-6">
      {/* Title */}
      <h2 className="text-lg font-bold mb-6">
        {t('BedDetails.title', 'Details')}
      </h2>

      {/* Card grid */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {bedDetails.map(
          ({ label1Key, value1Key, label2Key, value2Key, icon }, idx) => (
            <article
              key={idx}
              className="flex items-start gap-4 border border-gray-200 rounded-lg shadow-sm p-6"
            >

              <div className="grow text-sm space-y-3">
                <div className="flex justify-between">
                  <span className="pl-7 text-muted-foreground">
                    {t(label1Key)}:
                  </span>
                  <span className="font-semibold mr-20">{value1Key}</span>
                </div>
                {icon}
                <div className="flex justify-between mt-0 pt-0">
                  <span className="pl-7 text-muted-foreground">
                    {t(label2Key)}:
                  </span>
                  <span className="font-semibold mr-20">{value2Key}</span>
                </div>
              </div>
            </article>
          ),
        )}
      </div>
    </section>
  );
};

export default BedDetails;
