import React, {useState} from 'react';
import { useTranslation } from 'react-i18next';
import HmisDashboard from '../../../geo-profile/HmisData';
import MonthFilter from '../../../../components/common/MonthFilter';

interface ProgramDataProps {
  facilityId?: string;
  selectedMonth?: string;
}

const ProgramData: React.FC<ProgramDataProps> = ({facilityId}) => {
  const { t } = useTranslation();
  const [selectedMonth, setSelectedMonth] = useState<string>("");

  return (
    <div className="py-6">
      <h2 className="text-lg font-bold mb-6">{t('common.programData')}</h2>
      <div className="flex justify-end mx-21"><MonthFilter onChange={(val) => setSelectedMonth(val)} /></div>
      <HmisDashboard facilityId={facilityId} selectedMonth={selectedMonth} />
    </div>
  );
};

export default ProgramData;

