import React from 'react';
import { Building2, HandHelping, UserLock } from 'lucide-react';
import { useTranslation } from 'react-i18next';

type Detail = {
  labelKey: string;
  icon: React.ReactNode;
  valueKey: string;
};

type AuthorityDetailsProps = {
  details: Detail[];
};

const AuthorityDetails: React.FC<AuthorityDetailsProps> = ({ details }) => {
  const { t } = useTranslation();

  return (
    <div className="w-full">
      <h2 className="text-lg font-bold mb-6">{t('details.title')}</h2>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-0">
        {details.map(({ labelKey, icon, valueKey }, index) => (
          <div
            key={index}
            className="border border-gray-200 rounded-lg shadow-md w-full"
          >
            <div className="flex flex-col p-6 text-sm py-4">
              <span className="text-muted-foreground px-5 text-base">{t(labelKey)}</span>
              {icon}
              <span className="text-primary text-base px-5">{t(valueKey)}</span>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

export default AuthorityDetails;
