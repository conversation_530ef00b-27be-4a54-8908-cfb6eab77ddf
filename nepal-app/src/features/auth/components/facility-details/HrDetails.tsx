import React from 'react';
import { useTranslation } from 'react-i18next';
import HrCard from '../../../../components/common/HRCard';
import HrIcon from '../../../../assets/icons/hr.svg?react';
import DoctorIcon from '../../../../assets/icons/doctor.svg?react';
import NurseIcon from '../../../../assets/icons/nurse.svg?react';
import ChwIcon from '../../../../assets/icons/chw.svg?react';

export const hrCategories = [
  {
    title: 'hrdetails.doctorInFacility',
    filter: 'Doctor',
    count: 119,
    icon: <DoctorIcon className="h-8 w-8 text-black-200" strokeWidth={1.5} />,
    innerClass: 'px-4 py-2',
    countColor: 'text-red-600',
  },
  {
    title: 'hrdetails.nurse',
    filter: 'Nurse',
    count: 309,
    icon: <NurseIcon className="h-8 w-8 text-black-200" strokeWidth={1.5} />,
    innerClass: 'px-4 py-2',
    countColor: 'text-blue-600',
  },
  {
    title: 'hrdetails.chw',
    filter: 'CHW',
    count: 2,
    icon: <ChwIcon className="h-8 w-8 text-black-200" strokeWidth={1.5} />,
    innerClass: 'px-4 py-2',
    countColor: 'text-blue-600',
  },
];

const HrDetails: React.FC = () => {
  const { t } = useTranslation();

  return (
    <>
      <h2 className="text-lg mt-6 font-bold mb-6">{t('hrdetails.hrData')}</h2>
      <div className="flex flex-row gap-4 items-center">
        {/* Total HR card */}
        <div className="w-1/4">
          <div className={`flex justify-evenly items-center`}>
            <div className="p-5">
              <div className={`text-[21.6px] leading-tight text-gray-400`}>{hrCategories[0].count}</div>
              <div className={`text-[12.6px] text-gray-500 tracking-widest uppercase mt-1`}>
                {t('hrdetails.total')}
              </div>
            </div>
            <div>
              <HrIcon className="h-8 w-8" />
            </div>
          </div>
        </div>

        {/* Grid of other HR categories */}
        <div className="flex-1 grid grid-cols-1 md:grid-cols-3 xl:grid-cols-3 gap-6">
          {hrCategories.slice(1).map((category) => (
            <HrCard key={category.title} {...category} title={t(category.title)} />
          ))}
        </div>
      </div>
    </>
  );
};

export default HrDetails;
