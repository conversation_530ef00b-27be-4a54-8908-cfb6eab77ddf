import React from 'react';
import { useTranslation } from 'react-i18next';
import StatCard from '../../components/common/StatCard';
import SectionWrapper from './SectionWrapper';

interface StaffData {
  [key: string]: number;
}

const StaffSection: React.FC = () => {
  const { t } = useTranslation();

  const data: StaffData = {
    doctor: 38,
    nursing: 100,
    paramedical: 40,
    chw: 24,
    mpw: 83,
    asha: 12,
    cho: 10,
    administrative: 5,
  };

  return (
    <SectionWrapper title={t('geoprofile.hr.title')}>

      <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-y-4">
        {Object.entries(data).map(([key, value]) => (
          <StatCard
            key={key}
            label={t(`geoprofile.hr.${key}`, key)}
            value={value}
          />
        ))}
      </div>
    </SectionWrapper>
  );
};

export default StaffSection;
