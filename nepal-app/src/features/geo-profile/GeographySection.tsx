import React from 'react';
import StatCard from '../../components/common/StatCard';
import { Users, Building2, MapPin, Home } from 'lucide-react';
import { useTranslation } from 'react-i18next';
import SectionWrapper from './SectionWrapper';

interface GeographyData {
  targetPopulation: string;
  districts: number;
  palikas: number;
  wards: number;
}

const GeographySection: React.FC = () => {
  const { t } = useTranslation();

  const data: GeographyData = {
    targetPopulation: '61,68,986',
    districts: 13,
    palikas: 119,
    wards: 1121,
  };

  const standardStats = [
    {
      label: t('geoprofile.geography.district'),
      filter: 'district',
      value: data.districts,
      icon: <Building2 className="w-8 h-8 text-black-600" strokeWidth={1.5} />,
    },
    {
      label: t('geoprofile.geography.palika'),
      filter: 'palika',
      value: data.palikas,
      icon: <MapPin className="w-8 h-8 text-black-600" strokeWidth={1.5} />,
    },
    {
      label: t('geoprofile.geography.ward'),
      filter: 'ward',
      value: data.wards,
      icon: <Home className="w-8 h-8 text-black-600" strokeWidth={1.5} />,
    },
  ];

  return (
    <SectionWrapper title={t('geoprofile.geography.title')}>

      <div className="grid grid-cols-4 gap-0">
        {/* Target Population */}
        <div className="border border-gray-300 px-4 py-2 bg-white">
          <div className="flex items-center gap-10">
            <div className="flex items-center gap-3" aria-hidden="true">
              <Users className="w-8 h-8 text-black-600" strokeWidth={1.5} />
            </div>
            <div className="text-base font-bold text-red-500">
              {data.targetPopulation}
            </div>
          </div>
          <span className="text-xs text-gray-700">
            {t('geoprofile.geography.populationNote')}
          </span>
        </div>

        {/* Other Stats */}
        {standardStats.map(({ label, value, icon, filter }) => (
          <StatCard key={label} label={label} filter={filter} value={value} icon={icon} />
        ))}
      </div>
    </SectionWrapper>
  );
};

export default GeographySection;
