import React, { useState } from "react";
import { useTranslation } from "react-i18next";
import FilterBar from "../../components/common/MapFilters";
import GeoMap from "../../components/common/GeoMap";
import nepalOutline from "../../assets/maps/nepal-geo.json";
import provinceGeoJson from "../../assets/maps/nepal-provinces.geo.json";
import palikaGeoJson from "../../assets/maps/nepal-palika-geo.json"
import districtGeojson from "../../assets/maps/nepal-districts-geo.json";
import SectionWrapper from "./SectionWrapper";
import { healthFacilities } from "../../constants/healthFacility";

const GeoprofileMap: React.FC = () => {
    const FILTERS = [1, 2, 3, 4, 5, 6, 7] as const;
    type FilterType = typeof FILTERS[number];
    const [selectedFilter, setSelectedFilter] = useState<FilterType>(1);
    const { t } = useTranslation();
  
    
    const allowedProvinceId = 4;

    const filteredFacilities = healthFacilities.filter(facility => {
    return (
        facility.Facility_Type === selectedFilter &&
        facility.Province_ID === allowedProvinceId
    );
    });
    
  return (
    <SectionWrapper title={t('geoprofile.map.title')}>
        <FilterBar
          selectedFilter={selectedFilter}
          setSelectedFilter={setSelectedFilter}
        />
        <GeoMap
          level="province"
          allowedRegionId={4}
          geoJsonOutline={nepalOutline}
          geoJsonDivision={provinceGeoJson}
          facilities={filteredFacilities}
          filterType={selectedFilter}
          onRegionClick={(code) => console.log("Clicked region:", code)}
          onMarkerClick={(facility) => console.log("Facility:", facility)}
        />
      <div></div>
    </SectionWrapper>
  );
};

export default GeoprofileMap;
