import React from 'react';
import { useTranslation } from 'react-i18next';
import StatCard from '../../components/common/StatCard';
import FilledMapPin from '../../components/common/FilledMapPin';
import SectionWrapper from './SectionWrapper';

interface FacilityData {
  basic: number;
  primary: number;
  secondary: number;
  tertiary: number;
  private: number;
  ayurveda: number;
  laboratory: number;
}

const FacilitySection: React.FC = () => {
  const { t } = useTranslation();

  const data: FacilityData = {
    basic: 1258,
    primary: 146,
    secondary: 7,
    tertiary: 35,
    private: 1497,
    ayurveda: 72,
    laboratory: 1,
  };

  const facilityStats = [
    { key: 'basic', value: data.basic, color: '#ef4444' },
    { key: 'primary', value: data.primary, color: '#f97316' },
    { key: 'secondary', value: data.secondary, color: '#eab308' },
    { key: 'tertiary', value: data.tertiary, color: '#22c55e' },
    { key: 'private', value: data.private, color: '#14b8a6' },
    { key: 'ayurveda', value: data.ayurveda, color: '#3b82f6' },
    { key: 'laboratory', value: data.laboratory, color: '#6b7280' },
  ];

  return (
    <SectionWrapper title={t('geoprofile.facility.title')}>

      <div className="grid grid-cols-4 gap-0 mb-0">
        {facilityStats.slice(0, 4).map(({ key, value, color }) => (
          <StatCard
            key={key}
            label={t(`geoprofile.facility.${key}`)}
            value={value}
            icon={<FilledMapPin color={color} />}
          />
        ))}
      </div>

      <div className="grid grid-cols-3 gap-0">
        {facilityStats.slice(4).map(({ key, value, color }) => (
          <StatCard
            key={key}
            label={t(`geoprofile.facility.${key}`)}
            value={value}
            icon={<FilledMapPin color={color} />}
          />
        ))}
      </div>
    </SectionWrapper>
  );
};

export default FacilitySection;
