import React from 'react';
import { useTranslation } from 'react-i18next';
import SectionWrapper from './SectionWrapper';

interface SpecializedUnitsData {
  fruD: number;
  fruF: number;
  dpD: number;
  dpF: number;
  nbsuD: number;
  nbsuF: number;
  sncuD: number;
  sncuF: number;
  hwcD: number;
  hwcF: number;
}

const SpecializedUnitsSection: React.FC = () => {
  const { t } = useTranslation();

  const data: SpecializedUnitsData = {
    fruD: 12,
    fruF: 16,
    dpD: 10,
    dpF: 14,
    nbsuD: 3,
    nbsuF: 2,
    sncuD: 5,
    sncuF: 4,
    hwcD: 1,
    hwcF: 1,
  };
  
  const unitStats = [
    { key: 'fru', d: data.fruD, f: data.fruF },
    { key: 'dp', d: data.dpD, f: data.dpF },
    { key: 'nbsu', d: data.nbsuD, f: data.nbsuF },
    { key: 'sncu', d: data.sncuD, f: data.sncuF },
    { key: 'hwc', d: data.hwcD, f: data.hwcF },
  ];

  return (
    <SectionWrapper title={t('geoprofile.specializedUnits.title')}>

      <div className="grid grid-cols-5 gap-8">
        {unitStats.map(({ key, d, f }) => (
          <div
            key={key}
            className="border border-gray-300 p-4 bg-gray-50"
          >
            <div className="flex justify-between mb-1">
              <span className="text-sm font-medium text-gray-700">
                {t(`geoprofile.specializedUnits.${key}D`)}
              </span>
              <span className="text-sm font-semibold text-red-500">{d}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-sm font-medium text-gray-700">
                {t(`geoprofile.specializedUnits.${key}F`)}
              </span>
              <span className="text-sm font-semibold text-red-500">{f}</span>
            </div>
          </div>
        ))}
      </div>
    </SectionWrapper>
  );
};

export default SpecializedUnitsSection;
