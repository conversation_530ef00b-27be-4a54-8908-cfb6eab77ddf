import { useEffect, useState } from "react";
import { useTranslation } from "react-i18next";
import SectionWrapper from "./SectionWrapper";
import HmisCard from "../../components/common/HmisCard";
import MonthFilter from "../../components/common/MonthFilter";
import { getImmunizationDataset } from "../../api/services/immunization.service";
import type { Filters, Level } from "../../components/common/GeoFilter";

interface StatItem {
  label: string;
  value: number | string;
}

interface HmisCardData {
  title: string;
  total?: number;
  items: StatItem[];
}

interface HmisDashboardProps {
  selectedMonth?: string;
  filters?: Filters;
  level?: Level;
  facilityId?: string;
}

function StatList({ items }: { items: StatItem[] }) {
  const { t } = useTranslation();
  return (
    <div className="mt-2 space-y-4 text-sm">
      {items.map((item, idx) => (
        <div key={idx} className="flex justify-between text-gray-800 border-b border-gray-300 pb-2">
          <span>{t(`geoprofile.hmis.${item.label}`, item.label)}</span>
          <span>{item.value}</span>
        </div>
      ))}
    </div>
  );
}

export default function HmisDashboard({ selectedMonth, filters, level, facilityId }: HmisDashboardProps) {
  const { t } = useTranslation();
  const [reportDate, setReportDate] = useState<string>("");
  const [loading, setLoading] = useState<boolean>(true);
  const [cards, setCards] = useState<HmisCardData[]>([]);

  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true);
  
        const query: Record<string, string | number> = {};
  
        if (selectedMonth) query.month = selectedMonth;
        if (filters?.province) query.province_id = filters?.province;
        if (filters?.district) query.district_id = filters?.district;
        if (filters?.municipality) query.municipality_id = filters?.municipality;
        if (filters?.ward) query.ward_id = filters.ward;
        if (facilityId) query.facility_id = facilityId;
        const response = await getImmunizationDataset(query);
        setReportDate(response.reportDate);
  
        const formattedCards: HmisCardData[] = response.data.map((program) => ({
          title: program.program,
          total: program.total,
          items: program.categories.map((category) => ({
            label: category.label,
            value: category.value,
          })),
        }));
  
        setCards(formattedCards);
      } catch (err) {
        console.error("Failed to fetch immunization data", err);
        setCards([]);
      } finally {
        setLoading(false);
      }
    };
  
    fetchData();
  }, [selectedMonth, filters, facilityId]);

  // const convertLabelToKey = (label: string) => {
  //   return label.toLowerCase().replace(/\s+/g, "_").replace(/[^a-z0-9_]/g, "");
  // };

  return (
    <main
      className="px-6 bg-white"
      aria-label={t("geoprofile.hmis.sectionLabel", "HMIS Dashboard")}
    >
      <header className="flex justify-between items-center mb-4">
        <p className="text-sm text-gray-600">
          {reportDate}
        </p>
        {/* <MonthFilter onChange={(val) => setSelectedMonth(val)} /> */}
      </header>

      {loading ? (
        <p className="text-center text-gray-500">Loading...</p>
      ) : (
        <div className="flex flex-wrap gap-0 align-center bg-white">
          {cards.map((card, idx) => (
            <div key={idx} className="flex flex-col justify-center basis-full md:basis-[48%]">
              <HmisCard title={t(`geoprofile.hmis.${card.title}`, card.title)} total={card.total}>
                <StatList items={card.items} />
              </HmisCard>
            </div>
          ))}
        </div>
      )}
    </main>
  );
}
