import React from "react";
import { useTranslation } from "react-i18next";
import SectionWrapper from "./SectionWrapper";

const stats = [
  { label: "enrolledOver30", value: 10 },
  { label: "screened", value: 10 },
  { label: "partiallyScreened", value: 1 },
  { label: "fullyScreened", value: 9 },
  { label: "rescreened", value: 1 },
  { label: "referredByScreening", value: 2 },
  { label: "diagnosed", value: 8 },
  { label: "underTreatment", value: 17 },
  { label: "followUpAdherence", value: 2 },
];

export const NCDStats: React.FC = () => {
  const { t } = useTranslation();

  return (
    <SectionWrapper title={t('geoprofile.ncd.title')}>
      <p className="text-sm text-gray-600 mb-4">
        {t("geoprofile.ncd.period", "Nov 2024")}
      </p>

      <div className="rounded-xl border border-gray-300 bg-gray-50 p-6">
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-y-6 gap-x-8">
          {stats.map((item, index) => (
            <div key={index} className="flex flex-col">
              <span className="text-sm font-medium text-gray-700">
                {t(`geoprofile.ncd.${item.label}`, item.label)}
              </span>
              <span className="text-lg text-gray-800">{item.value}</span>
            </div>
          ))}
        </div>
      </div>
    </SectionWrapper>
  );
};
