import React, { useState, useEffect } from "react";
import GeoMap from "../../../components/common/GeoMap";
import nepalOutline from "../../../assets/maps/nepal-geo.json";
import provinceGeoJson from "../../../assets/maps/nepal-provinces.geo.json";
import districtGeoJson from "../../../assets/maps/nepal-districts-geo.json";
import palikaGeoJson from "../../../assets/maps/nepal-palika-geo.json";
import { useTranslation } from "react-i18next";
import DashboardSectionWrapper from "../../dashboard/components/DashboardSectionWrapper";
import type { Filters, Level } from "../../../components/common/GeoFilter";
import { getVaccineStorageCenters } from "../../../api/services/immunization.service";
import { getFlatLocationData } from "../../../api/services/location.service";
import Loader from "../../../components/common/Loader";

interface VaccineStorageMapProps {
  filters: Filters;
  level: Level;
}

const VaccineStorageMap: React.FC<VaccineStorageMapProps> = ({ filters, level }) => {
  const [healthFacilities, setHealthFacilities] = useState<any[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [flatLocations, setFlatLocations] = useState<{
    provinces: any[];
    districts: any[];
    municipalities: any[];
  }>({
    provinces: [],
    districts: [],
    municipalities: [],
  });

  const { t } = useTranslation();

  useEffect(() => {
    const fetchLocations = async () => {
      try {
        const data = await getFlatLocationData();
        setFlatLocations({
          provinces: data.provinces,
          districts: data.districts,
          municipalities: data.municipalities,
        });
      } catch (error) {
        console.error("Error fetching flat location data:", error);
      }
    };

    fetchLocations();
  }, []);

  useEffect(() => {
    const locationsReady =
      flatLocations.provinces.length > 0 &&
      flatLocations.districts.length > 0 &&
      flatLocations.municipalities.length > 0;

    const filtersExist =
      filters.province !== null || filters.district !== null || filters.municipality !== null;

    if (!locationsReady && filtersExist) return;

    const fetchFacilities = async () => {
      setLoading(true);
      try {
        const response = await getVaccineStorageCenters({
          province_id: filters.province || undefined,
          district_id: filters.district || undefined,
          municipality_id: filters.municipality || undefined,
        });
        setHealthFacilities(response.results);
      } catch (error) {
        console.error("Error fetching vaccine storage centers:", error);
      } finally {
        setLoading(false);
      }
    };

    fetchFacilities();
  }, [
    filters.province,
    filters.district,
    filters.municipality,
    flatLocations.provinces.length,
    flatLocations.districts.length,
    flatLocations.municipalities.length,
  ]);

  // const getGeoFilteredFacilities = () => {
  //   if (level === "province" && filters.province !== null) {
  //     return healthFacilities.filter(f => f.province === filters.province);
  //   }

  //   if (level === "district" && filters.district !== null) {
  //     return healthFacilities.filter(f => f.district === filters.district);
  //   }

  //   if (level === "municipality" && filters.municipality !== null) {
  //     return healthFacilities.filter(f => f.municipality === filters.municipality);
  //   }

  //   return healthFacilities;
  // };

  const getGeoJsonOutline = () => {
    if (level === "province") return provinceGeoJson;
    if (level === "district") return districtGeoJson;
    if (level === "municipality") return palikaGeoJson;
    return nepalOutline;
  };

  const getGeoJsonDivision = () => {
    if (level === "province") return districtGeoJson;
    if (level === "district") return palikaGeoJson;
    return provinceGeoJson;
  };

  const getAllowedRegionId = () => {
    if (level === "province") return filters.province ?? undefined;
    if (level === "district") return filters.district ?? undefined;
    if (level === "municipality") return filters.municipality ?? undefined;
    return undefined;
  };

  const filteredFacilities = healthFacilities;

  return (
    <DashboardSectionWrapper title={t("dashboard.mapData")}>
      <div className="w-full px-4 pt-4">
        <GeoMap
          level={level}
          allowedRegionId={getAllowedRegionId()}
          geoJsonOutline={getGeoJsonOutline()}
          geoJsonDivision={getGeoJsonDivision()}
          facilities={filteredFacilities}
          onRegionClick={(code) => console.log("Clicked region:", code)}
          onMarkerClick={(facility) => console.log("Clicked facility:", facility.original)}
        />
        {/* <Loader isLoading={loading} /> */}
      </div>
    </DashboardSectionWrapper>
  );
};

export default VaccineStorageMap;
