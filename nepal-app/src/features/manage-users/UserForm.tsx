import React, { useEffect, useState } from 'react';
import type { FormEvent } from 'react';
import { useTranslation } from 'react-i18next';
import { CircleX } from 'lucide-react';
import Input from '../../components/common/Input';
import {
  validateUsername,
  validateEmail,
} from '../../utils/validators';
import { getFlatLocationData } from '../../api/services/location.service';
import { useLocation } from '../../context/LocationContext';

type Status = 'active' | 'inactive' | 'disabled' | 'invited';
type Role = 'center' | 'province' | 'district' | 'municipality' | 'ward';

export interface UserFormData {
  fname: string;
  mname?: string;
  lname: string;
  email: string;
  username: string;
  phone?: string;
  role: Role;
  position_id: number;
  status?: Status;
  authority_location_id?: number;
  province_id?: number;
  district_id?: number;
  municipality_id?: number;
  ward_id?: number;
}

interface UserFormProps {
  mode: 'create' | 'edit';
  defaultValues?: Partial<UserFormData>;
  onSubmit: (user: UserFormData) => void;
  onCancel: () => void;
}

const roleLocationKeyMap: Record<Role, 'provinces' | 'districts' | 'municipalities' | 'wards'> = {
  center: 'provinces', // won't be used
  province: 'provinces',
  district: 'districts',
  municipality: 'municipalities',
  ward: 'wards',
};

const UserForm: React.FC<UserFormProps> = ({ mode, defaultValues, onCancel, onSubmit }) => {
  const { t } = useTranslation();

  const [formData, setFormData] = useState<UserFormData>({
    fname: '',
    mname: '',
    lname: '',
    email: '',
    username: '',
    phone: '',
    position_id: 1,
    role: 'province',
    status: 'active',
    authority_location_id: undefined,
  });

  const [locationData, setLocationData] = useState<{
    provinces: { id: number; name: string }[];
    districts: { id: number; name: string }[];
    municipalities: { id: number; name: string }[];
    wards: { id: number; name: string }[];
  }>({
    provinces: [],
    districts: [],
    municipalities: [],
    wards: [],
  });

  const [emailErrors, setEmailErrors] = useState<string[]>([]);
  const [usernameErrors, setUsernameErrors] = useState<string[]>([]);

  useEffect(() => {
    if (defaultValues) {
      setFormData((prev) => ({
        ...prev,
        ...defaultValues,
        mname: defaultValues.mname ?? '',
        phone: defaultValues.phone ?? '',
        position_id: defaultValues.position_id ?? 1,
      }));
    }
  }, [defaultValues]);

  useEffect(() => {
    if (formData.role !== 'center') {
      getFlatLocationData().then((data) => {
        setLocationData(data);
      });
    } else {
      setLocationData({
        provinces: [],
        districts: [],
        municipalities: [],
        wards: [],
      });
      setFormData((prev) => ({
        ...prev,
        authority_location_id: undefined,
      }));
    }
  }, [formData.role]);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value } = e.target;

    const newValue =
      name === 'position_id' || name === 'authority_location_id'
        ? value === '' ? undefined : Number(value)
        : value;

    setFormData((prev) => ({
      ...prev,
      [name]: newValue,
    }));

    if (name === 'email') setEmailErrors([]);
    if (name === 'username') setUsernameErrors([]);
  };

  const handleBlur = (e: React.FocusEvent<HTMLInputElement>) => {
    const { name, value } = e.target;

    if (name === 'email') setEmailErrors(validateEmail(value).errors);
    if (name === 'username') setUsernameErrors(validateUsername(value).errors);
  };

  const handleSubmit = (e: FormEvent) => {
    e.preventDefault();

    const emailValidation = validateEmail(formData.email);
    const usernameValidation = validateUsername(formData.username);

    setEmailErrors(emailValidation.errors);
    setUsernameErrors(usernameValidation.errors);

    const allValid =
      emailValidation.isValid &&
      usernameValidation.isValid;

    if (!allValid) return;

    const payload = { ...formData };
    onSubmit(payload);
  };

  return (
    <div className="mb-6">
      <div className="flex items-center justify-between mb-6">
        <h2 className="text-2xl font-semibold">{t('user.userTitle', 'User')}</h2>
        <button onClick={onCancel} className="text-red-500 hover:text-red-700">
          <CircleX size={24} />
        </button>
      </div>

      <form onSubmit={handleSubmit} className="max-w-2xl mx-auto p-6 bg-gray-100 rounded shadow-md space-y-4">
        <h2 className="text-2xl font-semibold text-center">
          {mode === 'create' ? t('user.registrationTitle') : t('user.editTitle')}
        </h2>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <Input name="fname" label={t('user.firstName') + ' *'} value={formData.fname} onChange={handleChange} required />
          <Input name="mname" label={t('user.middleName')} value={formData.mname || ''} onChange={handleChange} />
          <Input name="lname" label={t('user.lastName') + ' *'} value={formData.lname} onChange={handleChange} required />
          <Input name="phone" label={t('user.phone')} value={formData.phone || ''} onChange={handleChange} />

          <Input
            name="email"
            type="email"
            label={t('user.email') + ' *'}
            value={formData.email}
            onChange={handleChange}
            onBlur={handleBlur}
            required
          />
          {emailErrors.length > 0 && (
            <ul className="text-red-500 text-xs mt-1 list-disc pl-4 col-span-2">
              {emailErrors.map((err, idx) => <li key={idx}>{err}</li>)}
            </ul>
          )}

          <Input
            name="username"
            label={t('user.username') + ' *'}
            value={formData.username}
            onChange={handleChange}
            onBlur={handleBlur}
            required
          />
          {usernameErrors.length > 0 && (
            <ul className="text-red-500 text-xs mt-1 list-disc pl-4 col-span-2">
              {usernameErrors.map((err, idx) => <li key={idx}>{err}</li>)}
            </ul>
          )}

          <div className="mb-4 flex flex-col col-span-2">
            <label htmlFor="role" className="mb-1 text-xs font-bold text-[#0e2238]">
              {t('user.role')}
            </label>
            <select
              id="role"
              name="role"
              value={formData.role}
              onChange={handleChange}
              className="w-full rounded-lg border border-gray-300 bg-gray-50 px-3 py-2 text-xs focus:ring-1 focus:ring-blue-500"
            >
              <option value="center">{t('user.roleOptions.center')}</option>
              <option value="province">{t('user.roleOptions.province')}</option>
              <option value="district">{t('user.roleOptions.district')}</option>
              <option value="municipality">{t('user.roleOptions.municipality')}</option>
              <option value="ward">{t('user.roleOptions.ward')}</option>
            </select>
          </div>

          <div className="mb-4 flex flex-col col-span-2">
            <label htmlFor="position_id" className="mb-1 text-xs font-bold text-[#0e2238]">
              {t('user.position', 'Position')}
            </label>
            <select
              id="position_id"
              name="position_id"
              value={formData.position_id}
              onChange={handleChange}
              className="w-full rounded-lg border border-gray-300 bg-gray-50 px-3 py-2 text-xs focus:ring-1 focus:ring-blue-500"
            >
              <option value={1}>{t('user.positionOptions.admin', 'Admin')}</option>
              <option value={2}>{t('user.positionOptions.manager', 'Manager')}</option>
              <option value={3}>{t('user.positionOptions.staff', 'Staff')}</option>
            </select>
          </div>

          {/* Conditional dropdown for location */}
          {formData.role !== 'center' && (
            <div className="mb-4 flex flex-col col-span-2">
              <label htmlFor="authority_location_id" className="mb-1 text-xs font-bold text-[#0e2238]">
                {t(`common.${formData.role}`,`${formData.role}`)}
              </label>
              <select
                id="authority_location_id"
                name="authority_location_id"
                value={formData.authority_location_id || ''}
                onChange={handleChange}
                required
                className="w-full rounded-lg border border-gray-300 bg-gray-50 px-3 py-2 text-xs focus:ring-1 focus:ring-blue-500"
              >
                <option value="">{t('common.select', 'Select')}</option>
                {locationData[roleLocationKeyMap[formData.role]]?.map((item) => (
                  <option key={item.id} value={item.id}>
                    {item.name}
                  </option>
                ))}
              </select>
            </div>
          )}
        </div>

        <div className="flex justify-center space-x-4">
          <button type="submit" className="bg-blue-600 hover:bg-blue-700 text-white font-semibold py-2 px-4 rounded">
            {mode === 'create' ? t('user.submitButton') : t('user.updateButton')}
          </button>
          <button type="button" onClick={onCancel} className="bg-gray-300 hover:bg-gray-400 text-gray-800 font-semibold py-2 px-4 rounded">
            {t('common.cancel', 'Cancel')}
          </button>
        </div>
      </form>
    </div>
  );
};

export default UserForm;
