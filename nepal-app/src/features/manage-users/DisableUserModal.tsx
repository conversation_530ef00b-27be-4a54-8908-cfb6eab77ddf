import React, { useEffect, useState } from 'react';
import Modal from '../../components/common/Modal';
import Input from '../../components/common/Input';
import Button from '../../components/common/Button';
import { useTranslation } from 'react-i18next';
import { getUserById, disableUser } from '../../api/services/user.service'; // updated import
import type { UserFormData } from './UserForm';
import type { User } from '../../api/services/user.service';

interface DisableUserModalProps {
  isOpen: boolean;
  userId: number | null;
  userName: string;
  onClose: () => void;
  onSuccess: (userId: number) => void;
}

const DisableUserModal: React.FC<DisableUserModalProps> = ({
  isOpen,
  userId,
  userName,
  onClose,
  onSuccess,
}) => {
  const { t } = useTranslation();
  const [reason, setReason] = useState('');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [userData, setUserData] = useState<User | null>(null);

  useEffect(() => {
    if (isOpen && userId) {
      getUserById(userId)
        .then(setUserData)
        .catch(() => setError(t('manageusers.fetchError', 'Failed to fetch user data')));
    }
    if (!isOpen) {
      setReason('');
      setError(null);
      setLoading(false);
    }
  }, [isOpen, userId, t]);

  const handleSubmit = async () => {
    if (!userId || !userData) return;

    if (!reason.trim()) {
      setError(t('manageusers.reasonRequired', 'Reason is required'));
      return;
    }

    setLoading(true);
    setError(null);

    try {
      const payload = {
        disable_reason: reason,
      };

      await disableUser(userId, payload);

      onSuccess(userId);
      onClose();
    } catch (err) {
      console.error(err);
      setError(t('manageusers.disableFailed', 'Failed to disable user'));
    } finally {
      setLoading(false);
    }
  };

  return (
    <Modal
      isOpen={isOpen}
      onClose={onClose}
      title={t('manageusers.disableTitle', 'Disable User')}
    >
      <div className="space-y-4">
        <p>
          {t('manageusers.disablePrompt', {
            defaultValue: 'Please provide a reason to disable {{userName}}.',
            userName,
          })}
        </p>

        <Input
          type="text"
          placeholder={t('manageusers.disableReason', 'Reason')}
          value={reason}
          onChange={(e) => setReason(e.target.value)}
        />

        {error && <p className="text-red-600">{error}</p>}

        <div className="flex justify-end space-x-2">
          <Button onClick={onClose} className="bg-gray-300 text-black" disabled={loading}>
            {t('common.cancel', 'Cancel')}
          </Button>
          <Button
            onClick={handleSubmit}
            className="bg-red-600 text-white"
            disabled={loading}
          >
            {t('common.confirm', 'Confirm')}
          </Button>
        </div>
      </div>
    </Modal>
  );
};

export default DisableUserModal;
