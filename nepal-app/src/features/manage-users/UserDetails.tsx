import React, { useState } from 'react';
import { CircleXIcon, Trash2, Eye, Pencil } from 'lucide-react';
// import type { User } from './UserTable';
import type { User } from '../../api/services/user.service';


export type { User };

interface UserRowProps {
  user: User;
  highlight?: boolean;
  onEdit?: (user: User) => void;
  onDelete?: (user: User) => void;
  onView?: (user: User) => void;
}

const UserRow: React.FC<UserRowProps> = ({ user, highlight, onEdit, onDelete, onView }) => {
  const [showActions, setShowActions] = useState(false);

  const handleRowClick = () => {
    setShowActions((prev) => !prev);
  };

  return (
    <div
      className={`grid grid-cols-7 px-6 py-4 items-center text-sm border-t cursor-pointer ${
        highlight ? 'border-blue-500 border-t-2' : ''
      } hover:bg-gray-50`}
      onClick={handleRowClick}
    >
      <div className="col-span-1 text-blue-600">{user.fname} {user.lname}</div>
      <div className="col-span-1 capitalize">{user.role}</div>
      <div className="col-span-2 break-all">{user.email}</div>
      <div className="col-span-1">
        <span
          className={`text-xs px-2 py-1 rounded-full ${
            user.status === 'active'
              ? 'bg-emerald-100 text-emerald-700'
              : 'bg-gray-300 text-gray-600'
          }`}
        >
          {user.status}
        </span>
      </div>
      <div className="col-span-1">{user.createdOn}</div>
      <div className="col-span-1 flex items-center justify-end space-x-2">
        {showActions && (
          <>
            <button
              title="Edit"
              onClick={(e) => {
                e.stopPropagation();
                onEdit?.(user);
              }}
              className="text-yellow-500 hover:text-yellow-700"
            >
              <Pencil className="w-4 h-4" />
            </button>
            <button
              title="View"
              onClick={(e) => {
                e.stopPropagation();
                onView?.(user);
              }}
              className="text-blue-500 hover:text-blue-700"
            >
              <Eye className="w-4 h-4" />
            </button>
            <button
              title="Delete"
              onClick={(e) => {
                e.stopPropagation();
                onDelete?.(user);
              }}
              className="text-red-500 hover:text-red-700"
            >
              <Trash2 className="w-4 h-4" />
            </button>
            <CircleXIcon className="w-4 h-4 text-gray-400" />
          </>
        )}
      </div>
    </div>
  );
};

export default UserRow;
