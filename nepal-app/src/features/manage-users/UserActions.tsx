// UserActions.tsx
import React from 'react';
import { Trash2, <PERSON>, Pencil } from 'lucide-react';
import type { User } from '../../api/services/user.service';
import { useProfile } from '../../context/ProfileContext';

interface UserActionsProps {
  user: User;
  onEdit?: (user: User) => void;
  onDelete?: (user: User) => void;
  onView?: (user: User) => void;
  onDisable?: (user: User) => void;
}

const UserActions: React.FC<UserActionsProps> = ({ user, onEdit, onDelete, onDisable, onView }) => {
  const { profile } = useProfile();

  const isCenterAdmin =
    profile?.authority_level === 'center' && profile?.position === 1;

  return (
    <div className="flex items-center justify-end space-x-2">
      {/* View button - always shown */}
      <button
        title="View"
        onClick={(e) => {
          e.stopPropagation();
          onView?.(user);
        }}
        className="text-blue-500 hover:text-blue-700"
      >
        <Eye className="w-4 h-4" />
      </button>

      {/* Conditionally render Edit and Disable */}
      {isCenterAdmin && (
        <>
          <button
            title="Edit"
            onClick={(e) => {
              e.stopPropagation();
              onEdit?.(user);
            }}
            className="text-yellow-500 hover:text-yellow-700"
          >
            <Pencil className="w-4 h-4" />
          </button>

          <button
            title="Disable"
            onClick={(e) => {
              e.stopPropagation();
              onDisable?.(user);
            }}
            className="text-red-500 hover:text-red-700"
          >
            <Trash2 className="w-4 h-4" />
          </button>
        </>
      )}
    </div>
  );
};

export default UserActions;
