import React from 'react';
import { useTranslation } from 'react-i18next';
import type { Column } from '../../components/common/Table';
import UserActions from './UserActions';
import type { User } from '../../api/services/user.service';
import { PaginatedTablee } from '../../components/common/PaginatedTablee';



export type { User };

interface UserTableProps {
  users: User[];
  total: number;
  page: number;
  pageSize: number;
  loading: boolean;
  onPageChange: (page: number) => void;
  onPageSizeChange: (size: number) => void;
  onEdit: (user: User) => void;
  onDelete: (user: User) => void;
  onView: (user: User) => void;
  onDisable: (user: User) => void;
}

const UserTable: React.FC<UserTableProps> = ({
  users,
  page,
  pageSize,
  total,
  loading,
  onPageChange,
  onPageSizeChange,
  onEdit,
  onDelete,
  onView,
  onDisable
}) => {
  const { t } = useTranslation();

  const columns: Column<User>[] = [
    {
      header: t('user.name'),
      accessor: 'fname',
      render: (_, row) => (
        <span>{[row.fname, row.mname, row.lname].filter(Boolean).join(' ')}</span>
      ),
    },
    {
      header: t('user.role'),
      accessor: 'role',
      render: (value) => t(`user.roleOptions.${value}`),
    },
    {
      header: t('user.email'),
      accessor: 'email',
    },
    {
      header: t('user.status'),
      accessor: 'status',
      render: (value) => {
        const statusMap: Record<string, { text: string; className: string }> = {
          invited: {
            text: t('user.statusInvited', 'Invited'),
            className: 'bg-green-100 text-green-700',
          },
          active: {
            text: t('user.statusActive', 'Active'),
            className: 'bg-blue-100 text-blue-700',
          },
          disabled: {
            text: t('user.statusDisabled', 'Disabled'),
            className: 'bg-red-100 text-red-700',
          },
        };

        const statusInfo = statusMap[value] ?? {
          text: value,
          className: 'bg-gray-100 text-gray-700',
        };

        return (
          <span
            className={`inline-block px-2 py-1 text-xs rounded-full ${statusInfo.className}`}
          >
            {statusInfo.text}
          </span>
        );
      },
    },
    {
      header: '',
      accessor: 'username',
      render: (_, user) => (
        <UserActions
          user={user}
          onEdit={onEdit}
          onDelete={onDelete}
          onView={onView}
          onDisable={onDisable}
        />
      ),
    },
  ];

  return (
    <PaginatedTablee<User>
      data={users}
      columns={columns}
      title={t('user.listTitle')}
      total={total}
      page={page}
      pageSize={pageSize}
      onPageChange={onPageChange}
      onPageSizeChange={onPageSizeChange}
      getRowKey={(row) => row.username ?? row.email}
    />
  );
};

export default UserTable;
