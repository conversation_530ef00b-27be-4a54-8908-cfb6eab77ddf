import {
    getProvinceById, 
    getDistrictById,
    getMunicipalityById,
    getWardById,
 } from "../api/services/location.service";
  
  /**
   * Fetch location name based on authority level and location ID.
   * @param level - The authority level e.g. 'province', 'district', 'municipality', 'ward', or 'center'
   * @param id - The location ID
   * @returns The name of the location or a fallback string
   */
  export const getLocationName = async (
    level: string,
    id: number | undefined
  ): Promise<string> => {
    if (!id) return '-';
  
    switch (level.toLowerCase()) {
      case 'province':
            const province = await getProvinceById(id);
            console.log(`province${province}`);
            return province?.name || '-';
  
      case 'district':
        const district = await getDistrictById(id);
        return district?.name || '-';
  
      case 'municipality':
        const municipality = await getMunicipalityById(id);
        return municipality?.name || '-';
  
      case 'ward':
        const ward = await getWardById(id);
        return ward?.name || '-';
  
      case 'center':
        return 'Center';
  
      default:
        return '-';
    }
  };
  