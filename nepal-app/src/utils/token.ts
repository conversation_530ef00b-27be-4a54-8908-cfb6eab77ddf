import {jwtDecode} from 'jwt-decode';

export interface DecodedToken {
  authority_level: 'center' | 'province' | 'district' | 'municipality' | 'ward';
  authority_location_id: number;
  authority_location_name: string;
  position_name: string;
  exp: number;
  [key: string]: any;
}

export const getDecodedToken = (): DecodedToken | null => {
  const token = localStorage.getItem('authToken');
  if (!token) return null;

  try {
    return jwtDecode<DecodedToken>(token);
  } catch (err) {
    console.error('Invalid token');
    return null;
  }
};
