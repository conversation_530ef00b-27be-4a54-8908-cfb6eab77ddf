import i18next from 'i18next';

export interface PasswordValidationResult {
  isValid: boolean;
  errors: string[];
}

export interface UsernameValidationResult {
  isValid: boolean;
  errors: string[];
}

export interface EmailValidationResult {
  isValid: boolean;
  errors: string[];
}

/**
 * Validate a password with translation support
 */
export const validatePassword = (password: string): PasswordValidationResult => {
  const t = i18next.t;
  const errors: string[] = [];

  if (password.length < 8) {
    errors.push(t('password.minLength'));
  }

  if (!/[A-Z]/.test(password)) {
    errors.push(t('password.uppercase'));
  }

  if (!/[a-z]/.test(password)) {
    errors.push(t('password.lowercase'));
  }

  if (!/[0-9]/.test(password)) {
    errors.push(t('password.number'));
  }

  if (!/[^A-Za-z0-9]/.test(password)) {
    errors.push(t('password.special'));
  }

  return {
    isValid: errors.length === 0,
    errors,
  };
};

/**
 * Validate username - no spaces allowed
 */
export const validateUsername = (username: string): UsernameValidationResult => {
  const t = i18next.t;
  const errors: string[] = [];

  if (/\s/.test(username)) {
    errors.push(t('username.noSpaces'));
  }

  return {
    isValid: errors.length === 0,
    errors,
  };
};

/**
 * Validate email with a simple regex
 */
export const validateEmail = (email: string): EmailValidationResult => {
  const t = i18next.t;
  const errors: string[] = [];

  // Simple email regex
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;

  if (!emailRegex.test(email)) {
    errors.push(t('email.invalid'));
  }

  return {
    isValid: errors.length === 0,
    errors,
  };
};

/**
 * Check if passwords match (UI handles translation)
 */
export const doPasswordsMatch = (password: string, confirmPassword: string): boolean => {
  return password === confirmPassword;
};
