// hrData.tsx

export type HrDataType = {
  sl_no: number;
  designation_name: string;
  hr_availability: string;
  type: 'doctor' | 'nurse' | 'chw';
};

export const hr_data: HrDataType[] = [
  {
    sl_no: 1,
    designation_name: "General Physician",
    hr_availability: "Available",
    type: "doctor",
  },
  {
    sl_no: 2,
    designation_name: "Cardiologist",
    hr_availability: "Not Available",
    type: "doctor",
  },
  {
    sl_no: 3,
    designation_name: "Orthopedic Surgeon",
    hr_availability: "Available from 5 June 2025",
    type: "doctor",
  },
  {
    sl_no: 4,
    designation_name: "Pediatrician",
    hr_availability: "Pending confirmation",
    type: "doctor",
  },
  {
    sl_no: 5,
    designation_name: "Dermatologist",
    hr_availability: "Available",
    type: "doctor",
  },
  {
    sl_no: 1,
    designation_name: "Staff Nurse",
    hr_availability: "Available",
    type: "nurse",
  },
  {
    sl_no: 2,
    designation_name: "I<PERSON> Nurse",
    hr_availability: "Not Available",
    type: "nurse",
  },
  {
    sl_no: 3,
    designation_name: "OT Nurse",
    hr_availability: "Available from 7 June 2025",
    type: "nurse",
  },
  {
    sl_no: 4,
    designation_name: "Head Nurse",
    hr_availability: "Pending confirmation",
    type: "nurse",
  },
  {
    sl_no: 5,
    designation_name: "GNM (General Nursing)",
    hr_availability: "Available",
    type: "nurse",
  },
  {
    sl_no: 1,
    designation_name: "chw - Community Health",
    hr_availability: "Available",
    type: "chw",
  },
  {
    sl_no: 2,
    designation_name: "chw - Rural Outreach",
    hr_availability: "Not Available",
    type: "chw",
  },
  {
    sl_no: 3,
    designation_name: "chw Supervisor",
    hr_availability: "Available from 6 June 2025",
    type: "chw",
  },
  {
    sl_no: 4,
    designation_name: "chw Trainee",
    hr_availability: "Pending confirmation",
    type: "chw",
  },
  {
    sl_no: 5,
    designation_name: "chw - Maternity Ward",
    hr_availability: "Available",
    type: "chw",
  }
];
