export const dummyNotifications = [
    {
      id: 1,
      type: "VACCINE_DELIVERED",
      data: { location: "Gandaki", quantity: 566 },
      isRead: false,
      time: "1 hour ago",
    },
    {
      id: 2,
      type: "IMMUNIZATION_COMPLETED",
      data: { location: "Kathmandu", count: 45 },
      isRead: false,
      time: "2 hours ago",
    },
    {
      id: 3,
      type: "STOCK_ALERT",
      data: { vaccine: "BCG", location: "Biratnagar", level: "Low" },
      isRead: false,
      time: "3 hours ago",
    },
    {
      id: 4,
      type: "REMINDER",
      data: { name: "<PERSON>", location: "Pokhara" },
      isRead: true,
      time: "6 hours ago",
    },
    {
      id: 5,
      type: "DATA_SYNC_COMPLETE",
      data: { location: "Province 1" },
      isRead: true,
      time: "8 hours ago",
    },
    {
      id: 6,
      type: "NEW_REGISTRATION",
      data: { name: "<PERSON>", age: 2 },
      isRead: false,
      time: "12 hours ago",
    },
    {
      id: 7,
      type: "IMMUNIZATION_CAMPAIGN_STARTED",
      data: { location: "<PERSON><PERSON><PERSON>", vaccine: "Measles" },
      isRead: true,
      time: "1 day ago",
    },
    {
      id: 8,
      type: "VACCINE_WASTAGE_ALERT",
      data: { location: "Janakpur", quantity: 18 },
      isRead: false,
      time: "1 day ago",
    },
    {
      id: 9,
      type: "VACCINE_DELIVERED",
      data: { location: "Bagmati", quantity: 912 },
      isRead: false,
      time: "2 days ago",
    },
    {
      id: 10,
      type: "IMMUNIZATION_COMPLETED",
      data: { location: "Chitwan", count: 137 },
      isRead: true,
      time: "3 days ago",
    },
    {
      id: 11,
      type: "SYSTEM_ALERT",
      data: { message: "New version of the app is available." },
      isRead: false,
      time: "3 days ago",
    },
    {
      id: 12,
      type: "NEW_IMMUNIZATION_RECORD",
      data: { name: "Suman Ghimire", vaccine: "Hepatitis B" },
      isRead: true,
      time: "4 days ago",
    },
  ];
  
  
  