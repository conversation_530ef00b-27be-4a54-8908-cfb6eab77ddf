export interface Facility {
    id: number;
    name: string;
    type: 'Basic' | 'Primary' | 'Secondary';
    provinceId: number;
    districtId: number;
    palikaId: number;
    wardId: number;
    code: string;
  }
  
  export const FACILITIES: Facility[] = [
    {
      id: 2032,
      name: 'Health Post A',
      type: 'Basic',
      provinceId: 1,
      districtId: 101,
      palikaId: 10101,
      wardId: 1010101,
      code: 'FAC001',
    },
    {
      id: 2833,
      name: 'Primary Hospital B',
      type: 'Primary',
      provinceId: 1,
      districtId: 102,
      palikaId: 10201,
      wardId: 1020101,
      code: 'FAC002',
    },
    {
      id: 2833,
      name: 'Secondary Hospital C',
      type: 'Secondary',
      provinceId: 2,
      districtId: 215,
      palikaId: 21501,
      wardId: 2150101,
      code: 'FAC003',
    },
    {
      id: 1941,
      name: 'Basic Clinic D',
      type: 'Basic',
      provinceId: 3,
      districtId: 328,
      palikaId: 32801,
      wardId: 3280102,
      code: 'FAC004',
    },
    {
      id: 1941,
      name: 'Primary Health Center E',
      type: 'Primary',
      provinceId: 3,
      districtId: 329,
      palikaId: 32901,
      wardId: 3290101,
      code: 'FAC005',
    },
    {
      id: 1941,
      name: 'Secondary Hospital F',
      type: 'Secondary',
      provinceId: 4,
      districtId: 436,
      palikaId: 43601,
      wardId: 4360102,
      code: 'FAC006',
    },
    {
        id: 1941,
        name: 'Basic Clinic D',
        type: 'Basic',
        provinceId: 3,
        districtId: 328,
        palikaId: 32801,
        wardId: 3280102,
        code: 'FAC004',
      },
      {
        id: 1887,
        name: 'Primary Health Center E',
        type: 'Primary',
        provinceId: 3,
        districtId: 329,
        palikaId: 32901,
        wardId: 3290101,
        code: 'FAC005',
      },
      {
        id: 1887,
        name: 'Secondary Hospital F',
        type: 'Secondary',
        provinceId: 4,
        districtId: 436,
        palikaId: 43601,
        wardId: 4360102,
        code: 'FAC006',
      },
      {
        id: 1887,
        name: 'Basic Clinic D',
        type: 'Basic',
        provinceId: 3,
        districtId: 328,
        palikaId: 32801,
        wardId: 3280102,
        code: 'FAC004',
      },
      {
        id: 1887,
        name: 'Primary Health Center E',
        type: 'Primary',
        provinceId: 3,
        districtId: 329,
        palikaId: 32901,
        wardId: 3290101,
        code: 'FAC005',
      },
      {
        id: 1887,
        name: 'Secondary Hospital F',
        type: 'Secondary',
        provinceId: 4,
        districtId: 436,
        palikaId: 43601,
        wardId: 4360102,
        code: 'FAC006',
      },
    
  ];
  