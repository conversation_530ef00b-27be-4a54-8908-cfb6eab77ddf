export interface Service {
    name: string;
    available: boolean | null;
  }
  
  export const serviceGroups: Service[][] = [
    [
      { name: 'FRU-D', available: true },
      { name: 'FRU-F', available: true },
    ],
    [
      { name: 'DP-D', available: true },
      { name: 'DP-F', available: true },
    ],
    [
      { name: 'NBSU-D', available: null },
      { name: 'NBSU-F', available: null },
    ],
    [
      { name: 'SNCU-D', available: true },
      { name: 'SNCU-F', available: true },
    ],
    [
      { name: 'HWC-D', available: false },
      { name: 'HWC-F', available: false },
    ],
  ];
  