export interface District {
  id: number;
  name: string;
  provinceId: number;
}

export const DISTRICTS: District[] = [
  // Province 1
  { id: 101, name: 'Taplejung', provinceId: 1 },
  { id: 102, name: '<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>', provinceId: 1 },
  { id: 103, name: 'Solukhumbu', provinceId: 1 },
  { id: 104, name: 'Okhaldhunga', provinceId: 1 },
  { id: 105, name: 'Khotang', provinceId: 1 },
  { id: 106, name: 'Bhojpur', provinceId: 1 },
  { id: 107, name: 'Dhankuta', provinceId: 1 },
  { id: 108, name: 'Terhathum', provinceId: 1 },
  { id: 109, name: 'Panchthar', provinceId: 1 },
  { id: 110, name: 'Ilam', provinceId: 1 },
  { id: 111, name: '<PERSON><PERSON><PERSON>', provinceId: 1 },
  { id: 112, name: 'Morang', provinceId: 1 },
  { id: 113, name: 'Sunsari', provinceId: 1 },
  { id: 114, name: 'Udayapur', provinceId: 1 },

  // Province 2
  { id: 215, name: '<PERSON><PERSON><PERSON>', provinceId: 2 },
  { id: 216, name: '<PERSON><PERSON><PERSON>', provinceId: 2 },
  { id: 217, name: 'Dhanusa', provinceId: 2 },
  { id: 218, name: 'Mahottari', provinceId: 2 },
  { id: 219, name: 'Sarlahi', provinceId: 2 },
  { id: 220, name: 'Rautahat', provinceId: 2 },
  { id: 221, name: 'Bara', provinceId: 2 },
  { id: 222, name: 'Parsa', provinceId: 2 },

  // Province 3
  { id: 323, name: 'Dolakha', provinceId: 3 },
  { id: 324, name: 'Sindhupalchok', provinceId: 3 },
  { id: 325, name: 'Rasuwa', provinceId: 3 },
  { id: 326, name: 'Dhading', provinceId: 3 },
  { id: 327, name: 'Nuwakot', provinceId: 3 },
  { id: 328, name: 'Kathmandu', provinceId: 3 },
  { id: 329, name: 'Bhaktapur', provinceId: 3 },
  { id: 330, name: 'Lalitpur', provinceId: 3 },
  { id: 331, name: 'Kavrepalanchok', provinceId: 3 },
  { id: 332, name: 'Ramechhap', provinceId: 3 },
  { id: 333, name: 'Sindhuli', provinceId: 3 },
  { id: 334, name: 'Makwanpur', provinceId: 3 },
  { id: 335, name: 'Chitawan', provinceId: 3 },

  // Province 4
  { id: 436, name: 'Gorkha', provinceId: 4 },
  { id: 437, name: 'Manang', provinceId: 4 },
  { id: 438, name: 'Mustang', provinceId: 4 },
  { id: 439, name: 'Myagdi', provinceId: 4 },
  { id: 440, name: 'Kaski', provinceId: 4 },
  { id: 441, name: 'Lamjung', provinceId: 4 },
  { id: 442, name: 'Tanahu', provinceId: 4 },
  { id: 443, name: 'Nawalparasi (Bardaghat Susta East)', provinceId: 4 },
  { id: 444, name: 'Syangja', provinceId: 4 },
  { id: 445, name: 'Parbat', provinceId: 4 },
  { id: 446, name: 'Baglung', provinceId: 4 },

  // Province 5
  { id: 547, name: 'Rukum (East)', provinceId: 5 },
  { id: 548, name: 'Rolpa', provinceId: 5 },
  { id: 549, name: 'Pyuthan', provinceId: 5 },
  { id: 550, name: 'Gulmi', provinceId: 5 },
  { id: 551, name: 'Arghakhanchi', provinceId: 5 },
  { id: 552, name: 'Palpa', provinceId: 5 },
  { id: 553, name: 'Nawalparasi (Bardaghat Susta West)', provinceId: 5 },
  { id: 554, name: 'Rupandehi', provinceId: 5 },
  { id: 555, name: 'Kapilbastu', provinceId: 5 },
  { id: 556, name: 'Dang', provinceId: 5 },
  { id: 557, name: 'Banke', provinceId: 5 },
  { id: 558, name: 'Bardiya', provinceId: 5 },

  // Province 6
  { id: 659, name: 'Dolpa', provinceId: 6 },
  { id: 660, name: 'Mugu', provinceId: 6 },
  { id: 661, name: 'Humla', provinceId: 6 },
  { id: 662, name: 'Jumla', provinceId: 6 },
  { id: 663, name: 'Kalikot', provinceId: 6 },
  { id: 664, name: 'Dailekh', provinceId: 6 },
  { id: 665, name: 'Jajarkot', provinceId: 6 },
  { id: 666, name: 'Rukum (West)', provinceId: 6 },
  { id: 667, name: 'Salyan', provinceId: 6 },
  { id: 668, name: 'Surkhet', provinceId: 6 },

  // Province 7
  { id: 769, name: 'Bajura', provinceId: 7 },
  { id: 770, name: 'Bajhang', provinceId: 7 },
  { id: 771, name: 'Darchula', provinceId: 7 },
  { id: 772, name: 'Baitadi', provinceId: 7 },
  { id: 773, name: 'Dadeldhura', provinceId: 7 },
  { id: 774, name: 'Doti', provinceId: 7 },
  { id: 775, name: 'Achham', provinceId: 7 },
  { id: 776, name: 'Kailali', provinceId: 7 },
  { id: 777, name: 'Kanchanpur', provinceId: 7 },
];
