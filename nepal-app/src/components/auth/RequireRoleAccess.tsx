import { Navigate, useLocation } from 'react-router-dom';
import { getDecodedToken } from '../../utils/token';

interface RequireRoleAccessProps {
  children: React.ReactNode;
  requiredAuthority?: string;
  requiredPosition?: string;
}

const RequireRoleAccess = ({
  children,
  requiredAuthority,
  requiredPosition,
}: RequireRoleAccessProps) => {
  const location = useLocation();
  const token = getDecodedToken();

  if (!token) {
    return <Navigate to="/login" state={{ from: location }} replace />;
  }

  if (token.exp && Date.now() >= token.exp * 1000) {
    return <Navigate to="/login" state={{ from: location }} replace />;
  }

  const hasAccess =
    (!requiredAuthority || token.authority_level === requiredAuthority) &&
    (!requiredPosition || token.position_name === requiredPosition);

  if (!hasAccess) {
    return <Navigate to="/unauthorized" state={{ from: location }} replace />;
  }

  return <>{children}</>;
};

export default RequireRoleAccess;
