import { Navigate, useLocation } from 'react-router-dom';
import { useProfile } from '../../context/ProfileContext';

interface RequireAccessProps {
  children: React.ReactNode;
  requiredAuthority?: string;
  requiredPosition?: string;
}

const RequireAccess = ({
  children,
  requiredAuthority,
  requiredPosition,
}: RequireAccessProps) => {
  const { profile, loading } = useProfile();
  const location = useLocation();

  if (loading) return null; // or show spinner

  const hasAccess =
    (!requiredAuthority || profile?.authority_level === requiredAuthority) &&
    (!requiredPosition || profile?.position_name === requiredPosition);

  if (!hasAccess) {
    return <Navigate to="/unauthorized" state={{ from: location }} replace />;
  }

  return children;
};

export default RequireAccess;
