import React, { useEffect, useRef } from 'react';
import * as Highcharts from 'highcharts';
import 'highcharts/modules/exporting';
// import 'highcharts/modules/accessibility';

interface DistrictData {
  district: string;
  total: number;
  basic: number;
  primary: number;
  secondary: number;
  tertiary: number;
  private: number;
  ayurveda: number;
  laboratory: number;
}

const PieChart: React.FC = () => {
  const chartRef = useRef<HTMLDivElement>(null);

  const data: DistrictData[] = [
    { district: "Bagmati", total: 3016, basic: 1258, primary: 146, secondary: 7, tertiary: 35, private: 1497, ayurveda: 72, laboratory: 1 },
    { district: "Gandaki", total: 1302, basic: 1027, primary: 24, secondary: 8, tertiary: 6, private: 163, ayurveda: 73, laboratory: 1 },
    { district: "Karnali", total: 0, basic: 0, primary: 0, secondary: 0, tertiary: 0, private: 0, ayurveda: 0, laboratory: 0 },
    { district: "Koshi", total: 0, basic: 0, primary: 0, secondary: 0, tertiary: 0, private: 0, ayurveda: 0, laboratory: 0 },
    { district: "Lumbini", total: 0, basic: 0, primary: 0, secondary: 0, tertiary: 0, private: 0, ayurveda: 0, laboratory: 0 },
    { district: "Madhesh", total: 0, basic: 0, primary: 0, secondary: 0, tertiary: 0, private: 0, ayurveda: 0, laboratory: 0 },
    { district: "Sudurpaschim", total: 0, basic: 0, primary: 0, secondary: 0, tertiary: 0, private: 0, ayurveda: 0, laboratory: 0 }
  ];

  useEffect(() => {
    if (!chartRef.current) return;

    const facilityTotals = {
      basic: 0,
      primary: 0,
      secondary: 0,
      tertiary: 0,
      private: 0,
      ayurveda: 0,
      laboratory: 0
    };

    data.forEach(district => {
      facilityTotals.basic += district.basic;
      facilityTotals.primary += district.primary;
      facilityTotals.secondary += district.secondary;
      facilityTotals.tertiary += district.tertiary;
      facilityTotals.private += district.private;
      facilityTotals.ayurveda += district.ayurveda;
      facilityTotals.laboratory += district.laboratory;
    });

    const pieData = [
      { name: 'Basic', y: facilityTotals.basic, color: '#3B82F6' },
      { name: 'Private', y: facilityTotals.private, color: '#6366F1' },
      { name: 'Primary', y: facilityTotals.primary, color: '#8B5CF6' },
      { name: 'Ayurveda', y: facilityTotals.ayurveda, color: '#EC4899' },
      { name: 'Tertiary', y: facilityTotals.tertiary, color: '#F59E0B' },
      { name: 'Secondary', y: facilityTotals.secondary, color: '#10B981' },
      { name: 'Laboratory', y: facilityTotals.laboratory, color: '#EF4444' }
    ].filter(item => item.y > 0);

    const chart = Highcharts.chart(chartRef.current, {
      chart: {
        type: 'pie',
        backgroundColor: 'transparent',
        height: 200
      },
      title: {
        text: 'Facility Type Wise Distribution',
        style: {
          fontSize: '18px',
          fontWeight: 'bold',
          color: '#1F2937'
        }
      },
      tooltip: {
        pointFormat: '<b>{point.name}</b><br/>' +
          'Count: <b>{point.y}</b><br/>' +
          'Percentage: <b>{point.percentage:.1f}%</b>',
        backgroundColor: 'rgba(255, 255, 255, 0.95)',
        borderColor: '#E5E7EB',
        borderRadius: 8,
        shadow: true
      },
      accessibility: {
        enabled: true,
        point: {
          valueSuffix: '%'
        }
      },
      plotOptions: {
        pie: {
          allowPointSelect: true,
          cursor: 'pointer',
          dataLabels: {
            enabled: true,
            format: '<b>{point.name}</b><br>{point.percentage:.1f}%',
            style: {
              fontSize: '9px',
              fontWeight: 'normal',
              color: '#374151',
            },
            distance: 20
          },
          showInLegend: false,
          size: '80%',
          innerSize: '40%',
          borderWidth: 2,
          borderColor: '#FFFFFF'
        }
      },
      series: [{
        name: 'Facilities',
        type: 'pie',
        data: pieData
      }],
      exporting: {
        enabled: true
      },
      credits: {
        enabled: false
      }
    });

    return () => {
      chart.destroy();
    };
  }, []);

  return (
    <div ref={chartRef} className="w-full max-w-4xl mx-auto p-0 bg-white rounded-lg shadow-lg" />
  );
};

export default PieChart;
