import React, { useEffect, useRef, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import LanguageSwitcher from '../common/LanguageSwitcher';
import NotificationBell from '../common/NotificationBell';
import NotificationModal from '../common/NotificationModal';
import AuthorityInfo from '../common/AuthorityInfo';


interface NavbarProps {
  userInfo?: {
    name: string;
    avatar: string;
  };
}

const Navbar: React.FC<NavbarProps> = ({
}) => {
  const [notificationOpen, setNotificationOpen] = useState(false);
  const notificationRef = useRef<HTMLDivElement | null>(null);
  const bellButtonRef = useRef<HTMLButtonElement | null>(null);
  const navigate = useNavigate();
  const { t } = useTranslation();

  const handleChangePassword = () => {
    navigate('/change-password');
  };

  const handleSignOut = () => {
    console.log('Sign out clicked');
  };

  // Manage Escape key and focus return
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.key === 'Escape') {
        setNotificationOpen(false);
        bellButtonRef.current?.focus();
      }
    };
    if (notificationOpen) {
      document.addEventListener('keydown', handleKeyDown);
    }
    return () => {
      document.removeEventListener('keydown', handleKeyDown);
    };
  }, [notificationOpen]);

  // Move focus to modal when opened
  useEffect(() => {
    if (notificationOpen && notificationRef.current) {
      notificationRef.current.focus();
    }
  }, [notificationOpen]);

  return (
    <header className="flex items-center justify-between p-6 md:p-8 sticky top-0 bg-white z-50">
      <div>
        <h2 tabIndex={0} className="text-xl md:text-[1.375rem] font-semibold text-gray-800 m-0">
          {t('navbar.title')}
        </h2>
      </div>

      <div className="flex items-center gap-4">
        <AuthorityInfo />
        <div className="w-px h-6 bg-gray-300" />
        <LanguageSwitcher />
        <div className="w-px h-6 bg-gray-300" />


        <button
          ref={bellButtonRef}
          type="button"
          className="focus:outline-none focus-visible:ring-2 focus-visible:ring-blue-500 rounded"
          aria-label={t('navbar.toggleNotifications')}
          aria-haspopup="dialog"
          aria-expanded={notificationOpen}
          onClick={() => setNotificationOpen(!notificationOpen)}
        >
          <NotificationBell />
        </button>

        {notificationOpen && (
          <>
            {/* Backdrop */}
            <div
              className="fixed inset-0 z-[999]"
              role="presentation"
              aria-hidden="true"
              onClick={() => {
                setNotificationOpen(false);
                bellButtonRef.current?.focus();
              }}
            />

            {/* Modal */}
            <div
              ref={notificationRef}
              className="absolute top-full right-0 mt-0 bg-white border border-gray-200 rounded-lg shadow-lg w-150 z-[1001] transition-all opacity-100 visible translate-y-0"
              role="dialog"
              aria-modal="true"
              aria-labelledby="notification-title"
              tabIndex={-1}
            >
              <h2 id="notification-title" className="sr-only">
                {t('navbar.notifications')}
              </h2>
              <NotificationModal
                notificationOpen={notificationOpen}
                setNotificationOpen={setNotificationOpen}
              />
            </div>
          </>
        )}
      </div>
    </header>
  );
};

export default Navbar;



