import React, { useState, useEffect } from 'react';
import { useNavigate, useLocation, Link } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import {
  MapIcon,
  MapPinnedIcon,
  LayoutDashboard,
  HospitalIcon,
  UsersIcon,
  ChevronRightIcon,
  ContactIcon,
  SyringeIcon
} from 'lucide-react';
import logo from '/Nepal.png';
import { useAuth } from '../../context/AuthContext';
import SidebarUserSettings from './SidebarUserSettings';
import { listFacilityTypes } from '../../api/services/facility.service';
import type { FacilityType } from '../../api/services/facility.service';
import { useProfile } from '../../context/ProfileContext';

const Sidebar: React.FC = () => {
  const { t } = useTranslation();
  const location = useLocation();
  const navigate = useNavigate();
  const { user } = useAuth();
  const currentPath = location.pathname;
  const { profile } = useProfile();

  const [geographyOpen, setGeographyOpen] = useState(false);
  const [facilityOpen, setFacilityOpen] = useState(false);
  const [hrOpen, setHrOpen] = useState(false);
  const [immunizationOpen, setImmunizationOpen] = useState(false);

  const toggleGeography = () => setGeographyOpen(prev => !prev);
  const toggleFacility = () => setFacilityOpen(prev => !prev);
  const toggleHR = () => setHrOpen(prev => !prev);
  const toggleImmunization = () => setImmunizationOpen(prev => !prev);
  const [facilityTypes, setFacilityTypes] = useState<FacilityType[]>([]);

  useEffect(() => {
    const fetchTypes = async () => {
      try {
        const data = await listFacilityTypes();
        setFacilityTypes(data);
      } catch (err) {
        console.error('Failed to load facility types', err);
      }
    };
    fetchTypes();
  }, []);
  

  const isActive = (path: string): boolean => {
    return currentPath === path || currentPath.startsWith(path + '/');
  };

  const isSubmenuActive = (category: string, filter: string): boolean => {
    const searchParams = new URLSearchParams(location.search);
    return (
      currentPath === '/report' &&
      searchParams.get('category') === category &&
      searchParams.get('filter') === filter
    );
  };
  

  const navLinkClass = (path: string): string =>
    `flex items-center px-6 py-2 transition-all text-[14.5px] font-normal ${
      isActive(path)
        ? 'bg-[#4650dd] text-white'
        : 'text-[#6c757d] hover:bg-[#00000008]'
    }`;

  const submenuClass = (isOpen: boolean): string =>
    `${isOpen ? 'block' : 'hidden'} pl-1 space-y-1`;

  const handleReportClick = (category: string, filter: string) => {
    navigate(`/report?category=${category}&filter=${filter}`);
  };

  const handleImmunizationClick = () => {
    navigate(`/vaccine-storage`)
  }

  const userInfo = { name: user?.name || 'User'};
  const userImage = 'https://via.placeholder.com/150';

  const handleChangePassword = () => console.log('Change password triggered');
  const handleSignOut = () => console.log('Sign out triggered');

  return (
    <nav
      className="fixed left-0 top-0 h-screen w-54 bg-white text-[#6c757d] shadow-lg flex flex-col z-50"
      aria-label="Sidebar Navigation"
    >
      <div className="px-6 py-4">
        <img src={logo} alt="App Logo" className="w-28" />
      </div>

      <div className="flex-1 overflow-y-auto no-scrollbar min-h-0">
        <ul className="space-y-2">
          {/* Dashboard */}
          <li>
            <Link
              to="/dashboard"
              className={navLinkClass('/dashboard')}
              aria-current={isActive('/dashboard') ? 'page' : undefined}
            >
              <MapIcon className="w-4 h-4" strokeWidth={1.5} aria-hidden="true" />
              <span className="ml-3">{t('sidebar.dashboard')}</span>
            </Link>
          </li>

          {/* Geo Profile */}
          <li>
            <Link
              to="/geo-profile"
              className={navLinkClass('/geo-profile')}
              aria-current={isActive('/geo-profile') ? 'page' : undefined}
            >
              <MapPinnedIcon className="w-4 h-4" strokeWidth={1.5} aria-hidden="true" />
              <span className="ml-3">{t('sidebar.geoProfile')}</span>
            </Link>
          </li>

          {/* Immunization */}
          <li>
            <button
              className="flex items-center w-full px-6 py-2 text-[14.5px] font-normal text-[#6c757d] hover:bg-[#00000008]"
              onClick={toggleImmunization}
              aria-expanded={immunizationOpen}
              aria-controls="immunization-submenu"
              aria-haspopup="true"
            >
              <SyringeIcon className="w-4 h-4" strokeWidth={1.5} aria-hidden="true" />
              <span className="ml-3 flex-1 text-left">{t('sidebar.immunization.immunization', 'Immunization')}</span>
              <ChevronIcon isOpen={immunizationOpen} />
            </button>
            <ul
              id="immunization-submenu"
              className={submenuClass(immunizationOpen)}
              role="menu"
              aria-label={t('sidebar.immunization.immunization', 'Immunization')}
            >
              {['VaccineStorage'].map(label => (
                <li key={label} role="none">
                  <button
                    role="menuitem"
                    onClick={() => handleImmunizationClick()}
                    className={`w-full text-left flex items-center px-6 py-2 transition-all text-xs font-normal ${
                      isActive('/vaccine-storage') ? 'bg-[#4650dd] text-white' : 'text-[#6c757d] hover:bg-blue-800/10'
                    }`}
                  >
                    <span className="relative before:content-[''] before:absolute before:left-0 before:top-1/2 before:-translate-y-1/2 before:bg-[#9ea9b4] before:rounded-md before:h-1.5 before:w-1.5 before:opacity-30 pl-6">
                      {t(`sidebar.immunization.${label.toLowerCase()}`, 'Vaccine Storage')}
                    </span>
                  </button>
                </li>
              ))}
            </ul>
          </li>
          
          {/* Population */}      
          <li>
            <Link
              to="/"
              className={navLinkClass('/')}
              aria-current={isActive('/') ? 'page' : undefined}
            >
              <UsersIcon className="w-4 h-4" strokeWidth={1.5} aria-hidden="true" />
              <span className="ml-3">{t('sidebar.population', 'Population')}</span>
            </Link>
          </li>

          {/* Geography */}
          {/* <li>
            <button
              className="flex items-center w-full px-6 py-2 text-[14.5px] font-normal text-[#6c757d] hover:bg-[#00000008]"
              onClick={toggleGeography}
              aria-expanded={geographyOpen}
              aria-controls="geography-submenu"
              aria-haspopup="true"
            >
              <LayoutDashboard className="w-4 h-4" strokeWidth={1.5} aria-hidden="true" />
              <span className="ml-3 flex-1 text-left">{t('sidebar.geography')}</span>
              <ChevronIcon isOpen={geographyOpen} />
            </button>
            <ul
              id="geography-submenu"
              className={submenuClass(geographyOpen)}
              role="menu"
              aria-label={t('sidebar.geography')}
            >
              {['Province', 'District', 'Palika', 'Ward'].map(item => (
                <li key={item} role="none">
                  <button
                    role="menuitem"
                    onClick={() => handleReportClick('Geography', item)}
                    className="w-full text-left flex items-center px-6 py-2 transition-all text-xs font-normal text-[#6c757d] hover:bg-[#00000008]"
                  >
                    <span className="relative before:content-[''] before:absolute before:left-0 before:top-1/2 before:-translate-y-1/2 before:bg-[#9ea9b4] before:rounded-md before:h-1.5 before:w-1.5 before:opacity-30 pl-6">
                      {t(`sidebar.geographyTypes.${item.toLowerCase()}`)}
                    </span>
                  </button>
                </li>
              ))}
            </ul>
          </li> */}

          <li>
            <Link
              to="/geography"
              className={navLinkClass('/geography')}
              aria-current={isActive('/geography') ? 'page' : undefined}
            >
              <LayoutDashboard className="w-4 h-4" strokeWidth={1.5} aria-hidden="true" />
              <span className="ml-3">{t('sidebar.geography')}</span>
            </Link>
          </li>

          {/* Facility */}

          <li>
            <Link
              to="/facility"
              className={navLinkClass('/facility')}
              aria-current={isActive('/facility') ? 'page' : undefined}
            >
              <HospitalIcon className="w-4 h-4" strokeWidth={1.5} aria-hidden="true" />
              <span className="ml-3">{t('sidebar.facility')}</span>
            </Link>
          </li>

          {/* HR */}
          <li>
            <button
              className="flex items-center w-full px-6 py-2 text-[14.5px] font-normal text-[#6c757d] hover:bg-[#00000008]"
              onClick={toggleHR}
              aria-expanded={hrOpen}
              aria-controls="hr-submenu"
              aria-haspopup="true"
            >
              <UsersIcon className="w-4 h-4" strokeWidth={1.5} aria-hidden="true" />
              <span className="ml-3 flex-1 text-left">{t('sidebar.hr.hr')}</span>
              <ChevronIcon isOpen={hrOpen} />
            </button>
            <ul
              id="hr-submenu"
              className={submenuClass(hrOpen)}
              role="menu"
              aria-label={t('sidebar.hr.hr')}
            >
              {['doctor', 'nurse', 'chw'].map(label => (
                <li key={label} role="none">
                  <button
                    role="menuitem"
                    onClick={() => handleReportClick('HR', label)}
                    className={`w-full text-left flex items-center px-6 py-2 transition-all text-xs font-normal ${
                      isSubmenuActive('HR', label) ? 'bg-[#4650dd] text-white' : 'text-[#6c757d] hover:bg-blue-800/10'
                    }`}
                  >
                    <span className="relative before:content-[''] before:absolute before:left-0 before:top-1/2 before:-translate-y-1/2 before:bg-[#9ea9b4] before:rounded-md before:h-1.5 before:w-1.5 before:opacity-30 pl-6">
                      {t(`sidebar.hr.${label.toLowerCase()}`)}
                    </span>
                  </button>
                </li>
              ))}
            </ul>
          </li>

          {/* User Management */}
          
        {profile?.authority_level === 'center' && (
          <li>
            <Link
              to="/manage-users"
              className={navLinkClass('/manage-users')}
              aria-current={isActive('/manage-users') ? 'page' : undefined}
            >
              <ContactIcon className="w-4 h-4" strokeWidth={1.5} aria-hidden="true" />
              <span className="ml-3">{t('sidebar.userManagement')}</span>
            </Link>
          </li>
        )}
        </ul>
      </div>

      {/* Bottom User Settings */}
      <div className="px-6 py-4 border-t border-gray-700">
        <SidebarUserSettings
          userInfo={userInfo}
          userImage={userImage}
          onChangePassword={handleChangePassword}
          onSignOut={handleSignOut}
        />
      </div>
    </nav>
  );
};

type ChevronIconProps = { isOpen: boolean };

const ChevronIcon: React.FC<ChevronIconProps> = ({ isOpen }) => (
  <ChevronRightIcon
    className={`w-4 h-4 ml-auto transition-transform ${isOpen ? 'rotate-90' : ''}`}
    strokeWidth={1.5}
    aria-hidden="true"
  />
);

export default Sidebar;
