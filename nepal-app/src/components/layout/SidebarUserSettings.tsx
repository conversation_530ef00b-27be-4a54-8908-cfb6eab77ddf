import React, { useState, useRef, useEffect } from 'react';
import { Users, ArrowRight } from 'lucide-react';
import UserMenu from '../common/UserMenu';

interface UserInfo {
  name: string;
}

interface SidebarUserSettingsProps {
  userInfo: UserInfo;
  userImage: string;
  onChangePassword: () => void;
  onSignOut: () => void;
}

const SidebarUserSettings: React.FC<SidebarUserSettingsProps> = ({
  userInfo,
  userImage,
  onChangePassword,
  onSignOut,
}) => {
  const [showModal, setShowModal] = useState(false);
  const containerRef = useRef<HTMLDivElement>(null);
  const buttonRef = useRef<HTMLButtonElement>(null);
  const wasModalOpen = useRef(false);

  useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      if (
        showModal &&
        containerRef.current &&
        !containerRef.current.contains(event.target as Node)
      ) {
        setShowModal(false);
      }
    }

    function handleKeyDown(event: KeyboardEvent) {
      if (event.key === 'Escape') {
        setShowModal(false);
      }
    }

    document.addEventListener('mousedown', handleClickOutside);
    document.addEventListener('keydown', handleKeyDown);

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
      document.removeEventListener('keydown', handleKeyDown);
    };
  }, [showModal]);

  useEffect(() => {
    if (!showModal && wasModalOpen.current && buttonRef.current) {
      buttonRef.current.focus();
    }
    wasModalOpen.current = showModal;
  }, [showModal]);

  return (
    <div className="relative" ref={containerRef}>
      <button
        ref={buttonRef}
        onClick={() => setShowModal(!showModal)}
        className="flex items-center justify-between gap-2 text-sm text-gray-400 hover:text-blue-400 w-full text-left"
        aria-haspopup="true"
        aria-expanded={showModal}
        aria-controls="user-menu"
        aria-label={`User menu for ${userInfo.name}`}
      >
        <div className="flex items-center gap-2">
          <Users className="w-4 h-4" />
          <span>{userInfo.name}</span>
        </div>
        <ArrowRight className="w-4 h-4" />
      </button>

      {showModal && (
        <>
          <div
            className="fixed inset-0 z-40"
            onClick={() => setShowModal(false)}
            aria-hidden="true"
          />
          <div
            id="user-menu"
            className="absolute -right-70 -top-30 -translate-y-1/4 mr-2 z-50 w-64"
            role="menu"
          >
            <UserMenu className="!min-w-full" />
          </div>
        </>
      )}
    </div>
  );
};

export default SidebarUserSettings;
