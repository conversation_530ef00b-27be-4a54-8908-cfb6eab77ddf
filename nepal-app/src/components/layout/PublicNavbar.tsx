import React from 'react';
import { useTranslation } from 'react-i18next';
import LanguageSwitcher from '../common/LanguageSwitcher';

const PublicTopbar: React.FC = () => {
  const { t } = useTranslation();

  return (
    <header
      role="banner"
      className="flex items-center justify-between p-6 md:p-8 sticky top-0 bg-white z-50"
    >
      <h1 className="text-xl md:text-[1.375rem] font-semibold text-gray-800 m-0">
        {t('common.appTitle')}
      </h1>
      <LanguageSwitcher />
    </header>
  );
};

export default PublicTopbar;
