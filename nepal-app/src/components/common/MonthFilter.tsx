import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';

interface MonthFilterProps {
  onChange?: (value: string) => void;
  className?: string;
}

const MonthFilter: React.FC<MonthFilterProps> = ({ onChange, className }) => {
  const { t, i18n } = useTranslation();

  const generateMonthOptions = (): { label: string; value: string }[] => {
    const options = [];
    const now = new Date();
    for (let i = 0; i < 24; i++) {
      const date = new Date(now.getFullYear(), now.getMonth() - i, 1);
      const label = date.toLocaleString(i18n.language, {
        month: 'long',
        year: 'numeric',
      });
      const value = `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}`;
      options.push({ label, value });
    }
    return options;
  };

  const [options] = useState(generateMonthOptions);
  const [selected, setSelected] = useState('');

  useEffect(() => {
    if (selected && onChange) {
      onChange(selected);
    }
  }, [selected, onChange]);

  const handleChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    setSelected(e.target.value);
  };

  return (
    <div className={`flex flex-col ${className}`}>
      <label htmlFor="month-filter" className="sr-only mb-1 text-sm font-medium text-gray-700">
        {t('monthFilter.label', 'Select Month')}
      </label>
      <select
        id="month-filter"
        value={selected}
        onChange={handleChange}
        className="appearance-none border border-gray-300 rounded px-2 py-1 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
        aria-describedby="month-filter-description"
      >
        <option value="" disabled hidden>
          {t('monthFilter.placeholder', 'Select month')}
        </option>
        {options.map(({ label, value }) => (
          <option key={value} value={value}>
            {label}
          </option>
        ))}
      </select>
      <span id="month-filter-description" className="sr-only">
        {t('monthFilter.description', 'Filter content by selected month')}
      </span>
    </div>
  );
};

export default MonthFilter;
