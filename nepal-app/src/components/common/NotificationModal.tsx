import { useEffect, useRef } from "react";
import { useNavigate } from "react-router-dom";
import { useTranslation } from "react-i18next";
import { dummyNotifications } from "../../constants/notifications";

type Props = {
  notificationOpen: boolean;
  setNotificationOpen: (open: boolean) => void;
};

export default function NotificationModal({
  notificationOpen,
  setNotificationOpen,
}: Props) {
  const { t } = useTranslation();
  const navigate = useNavigate();
  const modalRef = useRef<HTMLDivElement>(null);

  // Filter unread notifications
  const unreadNotifications = dummyNotifications.filter((n) => !n.isRead);

  // Close modal on Escape key press
  useEffect(() => {
    function handleKeyDown(event: KeyboardEvent) {
      if (event.key === "Escape") {
        setNotificationOpen(false);
      }
    }

    if (notificationOpen) {
      document.addEventListener("keydown", handleKeyDown);
    }

    return () => {
      document.removeEventListener("keydown", handleKeyDown);
    };
  }, [notificationOpen, setNotificationOpen]);

  // Trap focus inside modal when open
  useEffect(() => {
    if (!notificationOpen) return;

    const focusableElements = modalRef.current?.querySelectorAll<HTMLElement>(
      'a[href], button:not([disabled]), textarea, input, select, [tabindex]:not([tabindex="-1"])'
    );

    const firstElement = focusableElements?.[0];
    const lastElement = focusableElements?.[focusableElements.length - 1];

    function trapFocus(e: KeyboardEvent) {
      if (e.key !== "Tab") return;

      if (!firstElement || !lastElement) return;

      if (e.shiftKey) {
        // Shift + Tab
        if (document.activeElement === firstElement) {
          e.preventDefault();
          lastElement.focus();
        }
      } else {
        // Tab
        if (document.activeElement === lastElement) {
          e.preventDefault();
          firstElement.focus();
        }
      }
    }

    document.addEventListener("keydown", trapFocus);

    // Focus first element on open
    firstElement?.focus();

    return () => {
      document.removeEventListener("keydown", trapFocus);
    };
  }, [notificationOpen]);

  return (
    <>
      {notificationOpen && (
        <div
          role="dialog"
          aria-modal="true"
          aria-labelledby="notification-modal-title"
          tabIndex={-1}
          className="fixed inset-0 z-50 flex items-start justify-end p-4"
        >
          <div
            ref={modalRef}
            className="relative bg-white rounded-lg shadow-lg w-96 p-4 z-10 max-h-[80vh] overflow-y-auto"
          >
            <h2
              id="notification-modal-title"
              className="text-lg font-semibold mb-4"
            >
              {t("Notifications")}
            </h2>

            {unreadNotifications.length === 0 ? (
              <p className="text-gray-500">{t("notifications.noUnread")}</p>
            ) : (
              <ul className="space-y-3" role="list" aria-label={t("Notifications")}>
                {unreadNotifications.slice(0, 5).map((notif) => {
                  const message = t(`notifications.${notif.type}`, {
                    ...notif.data,
                    defaultValue: t("notifications.UNKNOWN"),
                  });

                  return (
                    <li
                      key={notif.id}
                      className="border-b pb-2 transition rounded-sm cursor-pointer bg-gray-50 text-gray-900 font-semibold"
                      tabIndex={0}
                      role="listitem"
                      aria-label={message}
                      onKeyDown={(e) => {
                        if (e.key === "Enter" || e.key === " ") {
                          // add behavior here if notifications are clickable
                        }
                      }}
                    >
                      <div className="flex items-start gap-2">
                        <span className="w-2 h-2 mt-1 bg-blue-600 rounded-full flex-shrink-0" />
                        <div className="flex-1">
                          <p className="text-sm">{message}</p>
                          <span className="text-xs text-gray-400">{notif.time}</span>
                        </div>
                      </div>
                    </li>
                  );
                })}
              </ul>
            )}

            <div className="mt-4 text-right">
              <button
                onClick={() => {
                  setNotificationOpen(false);
                  navigate("/notifications");
                }}
                className="text-sm text-blue-600 hover:underline focus:outline-none focus:ring-2 focus:ring-blue-500 rounded"
                aria-label={t("See All")}
              >
                {t("See All")}
              </button>
            </div>
          </div>
        </div>
      )}
    </>
  );
}
