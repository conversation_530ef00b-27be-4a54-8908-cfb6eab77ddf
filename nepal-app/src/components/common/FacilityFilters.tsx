import React, { useEffect, useRef, useState } from 'react';
import { useTranslation } from 'react-i18next';
import FilledMapPin from './FilledMapPin';
import { listFacilityTypes } from '../../api/services/facility.service';

export type FilterType = number;

type FilterBarProps = {
  selectedFilter: FilterType;
  setSelectedFilter: React.Dispatch<React.SetStateAction<FilterType>>;
};

type FacilityType = {
  id: number;
  name: string;
  code: string;
  description: string | null;
  
};

const COLORS: string[] = [
  '#ef4444', '#f97316', '#84cc16', '#3b82f6', '#6b7280',
  '#10b981', '#0284c7', '#9333ea', '#f59e0b', '#14b8a6'
];

export default function FilterBar({ selectedFilter, setSelectedFilter }: FilterBarProps) {
  const { t } = useTranslation();
  const buttonRefs = useRef<(HTMLButtonElement | null)[]>([]);
  const [facilityTypes, setFacilityTypes] = useState<FacilityType[]>([]);

  useEffect(() => {
    const fetchFacilityTypes = async () => {
      try {
        const data = await listFacilityTypes();
        setFacilityTypes(data);
      } catch (error) {
        console.error('Error fetching facility types:', error);
      }
    };
    fetchFacilityTypes();
  }, []);

  const handleKeyDown = (e: React.KeyboardEvent, index: number) => {
    let nextIndex = index;
    if (e.key === 'ArrowRight') {
      nextIndex = (index + 1) % facilityTypes.length;
    } else if (e.key === 'ArrowLeft') {
      nextIndex = (index - 1 + facilityTypes.length) % facilityTypes.length;
    } else {
      return;
    }
    e.preventDefault();
    setSelectedFilter(facilityTypes[nextIndex].id);
    buttonRefs.current[nextIndex]?.focus();
  };
    console.log(selectedFilter);

  return (
    <div
      className="flex justify-center gap-1 items-center my-4 flex-wrap"
      role="radiogroup"
      aria-label={t('filterBar.label')}
      >
      <h2 className="sr-only">{t('filterBar.label')}</h2>

      {facilityTypes.map((facility, index) => {
        const isSelected = selectedFilter === facility.id;
        const color = COLORS[index % COLORS.length];

        return (
          <button
            key={facility.id}
            ref={(el) => {
              buttonRefs.current[index] = el;
            }}
            role="radio"
            aria-checked={isSelected}
            aria-label={facility.name}
            tabIndex={isSelected ? 0 : -1}
            onClick={() => setSelectedFilter(facility.id)}
            onKeyDown={(e) => handleKeyDown(e, index)}
            className={`flex items-center gap-2 px-8 py-2 rounded-md focus:outline-none focus-visible:ring-2 focus-visible:ring-offset-2 focus-visible:ring-blue-500 ${
              isSelected
                ? 'bg-blue-600 text-white font-semibold shadow-md'
                : 'bg-transparent text-blue-700 hover:text-blue-800'
            }`}
          >
            <FilledMapPin size={30} color={color} dotColor="#fff" dotSize={8} />
            <span>{facility.name}</span>
          </button>
        );
      })}
    </div>
  );
}
