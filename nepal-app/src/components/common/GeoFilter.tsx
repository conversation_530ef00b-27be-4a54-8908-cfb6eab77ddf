import React, { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useLocation } from '../../context/LocationContext';
import { useProfile } from '../../context/ProfileContext';
import MonthFilter from './MonthFilter';

export type Level = 'country' | 'province' | 'district' | 'municipality' | 'ward';

export interface Filters {
  province: number | null;
  district: number | null;
  municipality: number | null;
  ward: number | null;
  country?: number | null;
}

interface GeoFilterProps {
  value: Filters;
  level: Level;
  onChange: (filters: Filters, level: Level) => void;
  monthFilter?: boolean;
  onMonthChange?: (month: string) => void;
}

const showSelect: Record<Level, Level[]> = {
  country: [],
  province: ['province'],
  district: ['province', 'district'],
  municipality: ['province', 'district', 'municipality'],
  ward: ['province', 'district', 'municipality', 'ward'],
};

const levelOrder: Level[] = ['country', 'province', 'district', 'municipality', 'ward'];

const GeoFilter: React.FC<GeoFilterProps> = ({ value, level, onChange, monthFilter, onMonthChange }) => {
  const { t } = useTranslation();
  const [selectedLevel, setSelectedLevel] = useState<Level>(level);
  const [filters, setFilters] = useState<Filters>(value);

  const { flatLocations } = useLocation();
  const { profile } = useProfile();

  const [provinces, setProvinces] = useState<any[]>([]);
  const [districts, setDistricts] = useState<any[]>([]);
  const [municipalities, setMunicipalities] = useState<any[]>([]);
  const [wards, setWards] = useState<any[]>([]);

  const [districtOptions, setDistrictOptions] = useState<any[]>([]);
  const [municipalityOptions, setMunicipalityOptions] = useState<any[]>([]);
  const [wardOptions, setWardOptions] = useState<any[]>([]);

  // Utility to check if a dropdown should be disabled based on authority_level
  const isDisabled = (fieldLevel: Level) => {
    if (!profile) return false;
    const userLevelIndex = levelOrder.indexOf(profile.authority_level as Level);
    const fieldLevelIndex = levelOrder.indexOf(fieldLevel);
    // disable if fieldLevel is above user authority (less specific level)
    return fieldLevelIndex < userLevelIndex;
  };

  // Filter location data based on user authority level and location id
  useEffect(() => {
    if (!flatLocations) return;
  
    // Early return for country-level users (no filtering)
    if (profile?.authority_level === 'country') {
      setProvinces(flatLocations.provinces || []);
      setDistricts(flatLocations.districts || []);
      setMunicipalities(flatLocations.municipalities || []);
      setWards(flatLocations.wards || []);
      return;
    }
  
    let filteredProvinces = flatLocations.provinces || [];
    let filteredDistricts = flatLocations.districts || [];
    let filteredMunicipalities = flatLocations.municipalities || [];
    let filteredWards = flatLocations.wards || [];
  
    if (profile) {
      const { authority_level, authority_location_id } = profile;
  
      switch (authority_level) {  
        case 'province':
          filteredProvinces = filteredProvinces.filter(p => p.id === authority_location_id);
          filteredDistricts = filteredDistricts.filter(d => d.province === authority_location_id);
          filteredMunicipalities = filteredMunicipalities.filter(m => {
            const district = flatLocations.districts.find(d => d.id === m.district);
            return district?.province === authority_location_id;
          });
          filteredWards = filteredWards.filter(w => {
            const municipality = flatLocations.municipalities.find(m => m.id === w.municipality);
            const district = flatLocations.districts.find(d => d.id === municipality?.district);
            return district?.province === authority_location_id;
          });
          break;

        case 'district':
          filteredDistricts = filteredDistricts.filter(d => d.id === authority_location_id);
          filteredMunicipalities = filteredMunicipalities.filter(m => m.district === authority_location_id);
          filteredWards = filteredWards.filter(w => {
            const municipality = flatLocations.municipalities.find(m => m.id === w.municipality);
            return municipality?.district === authority_location_id;
          });
          filteredProvinces = filteredProvinces.filter(p =>
            filteredDistricts.some(d => d.province === p.id)
          );
          break;

        case 'municipality':
          filteredMunicipalities = filteredMunicipalities.filter(m => m.id === authority_location_id);
          filteredWards = filteredWards.filter(w => w.municipality === authority_location_id);
          filteredDistricts = filteredDistricts.filter(d =>
            filteredMunicipalities.some(m => m.district === d.id)
          );
          filteredProvinces = filteredProvinces.filter(p =>
            filteredDistricts.some(d => d.province === p.id)
          );
          break;

        case 'ward':
          filteredWards = filteredWards.filter(w => w.id === authority_location_id);
          filteredMunicipalities = filteredMunicipalities.filter(m =>
            filteredWards.some(w => w.municipality === m.id)
          );
          filteredDistricts = filteredDistricts.filter(d =>
            filteredMunicipalities.some(m => m.district === d.id)
          );
          filteredProvinces = filteredProvinces.filter(p =>
            filteredDistricts.some(d => d.province === p.id)
          );
          break;
        case 'center':
        default:

        // country or others: no filtering
      }
    }

    setProvinces(filteredProvinces);
    setDistricts(filteredDistricts);
    setMunicipalities(filteredMunicipalities);
    setWards(filteredWards);
  }, [flatLocations, profile]);

  // Update district options when province changes
  useEffect(() => {
    if (filters.province !== null) {
      setDistrictOptions(districts.filter(d => d.province === filters.province));
    } else {
      setDistrictOptions(districts);
    }
  }, [filters.province, districts]);

  // Update municipality options when district changes
  useEffect(() => {
    if (filters.district !== null) {
      setMunicipalityOptions(municipalities.filter(m => m.district === filters.district));
    } else {
      setMunicipalityOptions(municipalities);
    }
  }, [filters.district, municipalities]);

  // Update ward options when municipality changes
  useEffect(() => {
    if (filters.municipality !== null) {
      setWardOptions(wards.filter(w => w.municipality === filters.municipality));
    } else {
      setWardOptions([]);
    }
  }, [filters.municipality, wards]);

  // Auto-fill locked filters (e.g. province locked to authority_location_id)
  useEffect(() => {
    if (!profile) return;

    const { authority_level, authority_location_id } = profile;

    if (authority_level === 'province' && filters.province !== authority_location_id) {
      const updatedFilters = {
        ...filters,
        province: authority_location_id,
        district: null,
        municipality: null,
        ward: null,
      };
      setFilters(updatedFilters);
      onChange(updatedFilters, selectedLevel);
    }

    if (authority_level === 'district' && filters.district !== authority_location_id) {
      const updatedFilters = {
        ...filters,
        province: districts.find(d => d.id === authority_location_id)?.province || null,
        district: authority_location_id,
        municipality: null,
        ward: null,
      };
      setFilters(updatedFilters);
      onChange(updatedFilters, selectedLevel);
    }

    if (authority_level === 'municipality' && filters.municipality !== authority_location_id) {
      const districtId = municipalities.find(m => m.id === authority_location_id)?.district || null;
      const provinceId = districts.find(d => d.id === districtId)?.province || null;

      const updatedFilters = {
        ...filters,
        province: provinceId,
        district: districtId,
        municipality: authority_location_id,
        ward: null,
      };
      setFilters(updatedFilters);
      onChange(updatedFilters, selectedLevel);
    }

    if (authority_level === 'ward' && filters.ward !== authority_location_id) {
      const municipalityId = wards.find(w => w.id === authority_location_id)?.municipality || null;
      const districtId = municipalities.find(m => m.id === municipalityId)?.district || null;
      const provinceId = districts.find(d => d.id === districtId)?.province || null;

      const updatedFilters = {
        ...filters,
        province: provinceId,
        district: districtId,
        municipality: municipalityId,
        ward: authority_location_id,
      };
      setFilters(updatedFilters);
      onChange(updatedFilters, selectedLevel);
    }
  }, [profile, districts, municipalities, wards]);

  useEffect(() => setSelectedLevel(level), [level]);
  useEffect(() => setFilters(value), [value]);

  // Find selected objects by id
  const selectedProvince = provinces.find(p => p.id === filters.province);
  const selectedDistrict = districts.find(d => d.id === filters.district);
  const selectedMunicipality = municipalities.find(m => m.id === filters.municipality);

  // Convert string to number or null on select change
  const parseId = (value: string): number | null => {
    const parsed = Number(value);
    return isNaN(parsed) ? null : parsed;
  };

  // Handle select change, store numbers or null
  const handleFilterChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    const { name, value } = e.target;
    let updated: Filters = { ...filters };

    const idValue = parseId(value);

    switch (name) {
      case 'province':
        updated.province = idValue;
        updated.district = null;
        updated.municipality = null;
        updated.ward = null;
        break;
      case 'district':
        updated.district = idValue;
        updated.municipality = null;
        updated.ward = null;
        break;
      case 'municipality':
        updated.municipality = idValue;
        updated.ward = null;
        break;
      case 'ward':
        updated.ward = idValue;
        break;
    }

    setFilters(updated);
    onChange(updated, selectedLevel);
  };

  const handleLevelChange = (newLevel: Level) => {
    setSelectedLevel(newLevel);
    onChange(filters, newLevel);
  };

  return (
    <fieldset className="flex flex-row gap-16 text-sm mb-4" role="radiogroup" aria-labelledby="geo-level-legend">
      <legend id="geo-level-legend" className="sr-only">
        {t('common.level')}
      </legend>
      {(Object.keys(showSelect) as Level[])
        .filter(levelOption => levelOption !== 'country')
        .map(levelOption => {
          const labelId = `level-${levelOption}`;
          const selectId = `select-${levelOption}`;

          // Get current selected value name for display
          const getSelectedName = () => {
            switch (levelOption) {
              case 'province':
                return provinces.find(p => p.id === filters.province)?.name || '';
              case 'district':
                return districts.find(d => d.id === filters.district)?.name || '';
              case 'municipality':
                return municipalities.find(m => m.id === filters.municipality)?.name || '';
              case 'ward':
                return wards.find(w => w.id === filters.ward)?.name || '';
              default:
                return '';
            }
          };

          return (
            <div key={levelOption} className="flex flex-col items-start gap-2">
              <label htmlFor={labelId} className="flex items-center w-50 gap-2">
                <input
                  id={labelId}
                  type="radio"
                  name="level"
                  value={levelOption}
                  checked={selectedLevel === levelOption}
                  onChange={() => handleLevelChange(levelOption)}
                  disabled={isDisabled(levelOption)}
                />
                <span className="capitalize">{t(`common.${levelOption}`)}</span>
              </label>

              {showSelect[selectedLevel].includes(levelOption) && (
                <select
                  id={selectId}
                  name={levelOption}
                  aria-label={t(`common.select${levelOption.charAt(0).toUpperCase() + levelOption.slice(1)}`)}
                  value={filters[levelOption] ?? ''}
                  onChange={handleFilterChange}
                  disabled={isDisabled(levelOption)}
                  className="appearance-none px-4 py-2 w-50 text-xs rounded-lg border border-[#edeef0] bg-[#f8fafb] font-normal text-[#0e2238] transition-all focus:border-blue-400 focus:outline-none focus:ring-1 focus:ring-blue-400"
                >
                  <option value="">
                    {t(`common.select${levelOption.charAt(0).toUpperCase() + levelOption.slice(1)}`)}
                  </option>

                  {levelOption === 'province' &&
                    provinces.map(p => (
                      <option key={p.id} value={p.id}>
                        {p.name}
                      </option>
                    ))}

                  {levelOption === 'district' &&
                    districtOptions.map(d => (
                      <option key={d.id} value={d.id}>
                        {d.name}
                      </option>
                    ))}

                  {levelOption === 'municipality' &&
                    municipalityOptions.map(m => (
                      <option key={m.id} value={m.id}>
                        {m.name}
                      </option>
                    ))}

                  {levelOption === 'ward' &&
                    wardOptions.map(w => (
                      <option key={w.id} value={w.id}>
                        {w.name}
                      </option>
                    ))}
                </select>
              )}
            </div>
          );
        })}
      {monthFilter && <MonthFilter onChange={val => onMonthChange?.(val)} />}
    </fieldset>
  );
};

export default GeoFilter;
