import React, { useEffect, useRef } from 'react';
import { FocusTrap } from 'focus-trap-react';
import { CircleX, CheckCircle } from 'lucide-react';

interface StatusModalProps {
  isOpen?: boolean;
  onClose?: () => void;
  onOk?: () => void;
  message?: string;
  type?: 'error' | 'success';
}

const StatusModal: React.FC<StatusModalProps> = ({
  isOpen = true,
  onClose,
  onOk,
  message = 'An unexpected error occurred.',
  type = 'error'
}) => {
  const okButtonRef = useRef<HTMLButtonElement>(null);

  useEffect(() => {
    if (isOpen) {
      okButtonRef.current?.focus();
    }
  }, [isOpen]);

  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.key === 'Escape') {
        onClose?.();
      }
    };
    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, [onClose]);

  if (!isOpen) return null;

  // Determine icon and colors based on type
  const isError = type === 'error';
  const Icon = isError ? CircleX : CheckCircle;
  const iconColor = isError ? 'text-red-400' : 'text-green-400';
  const buttonColor = isError ? 'bg-red-400 hover:bg-red-500 focus:ring-red-300' : 'bg-green-400 hover:bg-green-500 focus:ring-green-300';

  return (
    <div
      className="fixed inset-0 bg-black/50 flex items-center justify-center z-[1000]"
      role="alertdialog"
      aria-modal="true"
      aria-labelledby="status-modal-title"
      aria-describedby="status-modal-description"
    >
      <FocusTrap>
        <div
          className="bg-white text-center rounded-md shadow-xl p-6 w-[478px] max-w-[90vw] mx-4 animate-alert outline-none"
          tabIndex={-1}
        >
          {/* Hidden Close Button for accessibility */}
          <button
            onClick={onClose}
            aria-label="Close status dialog"
            className="sr-only"
          >
            Close
          </button>

          {/* Status Icon */}
          <div className="flex justify-center my-6">
            <Icon className={`w-20 h-20 ${iconColor}`} />
          </div>

          {/* Message */}
          <div className="mb-8">
            <h2 id="status-modal-title" className="sr-only">
              {type === 'error' ? 'Error' : 'Success'}
            </h2>
            <p
              id="status-modal-description"
              className="text-gray-500 text-base font-light"
            >
              {message}
            </p>
          </div>

          {/* OK Button */}
          <div className="flex justify-center">
            <button
              ref={okButtonRef}
              onClick={onOk}
              className={`text-white font-medium px-8 py-3 rounded-md text-base focus:outline-none focus:ring-2 ${buttonColor}`}
            >
              OK
            </button>
          </div>
        </div>
      </FocusTrap>
    </div>
  );
};

export default StatusModal;
