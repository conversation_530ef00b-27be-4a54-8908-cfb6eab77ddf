import React, { useState, useRef, useEffect } from 'react';
import { MoreVertical } from 'lucide-react';
import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router-dom';

interface HrCardProps {
  title: string;
  filter: string;
  count: number;
  icon: React.ReactNode;
  onViewList?: (filter: string) => void;
  countColor?: string;
  className?: string;
  innerClass?: string;
}

const HrCard: React.FC<HrCardProps> = ({
  title,
  filter,
  count,
  icon,
  onViewList,
  countColor,
  className,
  innerClass
}) => {
  const { t } = useTranslation();
  const [menuOpen, setMenuOpen] = useState(false);
  const menuRef = useRef<HTMLDivElement>(null);
  const navigate = useNavigate();

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (menuRef.current && !menuRef.current.contains(event.target as Node)) {
        setMenuOpen(false);
      }
    };
    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  const handleNavigate = (e: React.MouseEvent | React.KeyboardEvent) => {
    e.stopPropagation();
    navigate(`/report?category=HR&filter=${filter}`);
  };

  return (
    <div
      role="button"
      tabIndex={0}
      onClick={handleNavigate}
      onKeyDown={(e) => {
        if (e.key === 'Enter' || e.key === ' ') {
          handleNavigate(e);
        }
      }}
      aria-label={t('hrCard.viewReport', { title })}
      className={`relative w-full rounded-xl ${className} shadow-lg hover:shadow-xl transition duration-200 bg-white cursor-pointer hover:scale-[1.01] focus:outline-none focus-visible:ring-2 focus-visible:ring-blue-500`}
    >
      <div className={`flex justify-between items-center ${innerClass}`}>
        <div>
          <div className={`text-[21.6px] leading-tight ${countColor || 'text-gray-400'}`}>
            {count}
          </div>
          <div className="text-[12.6px] text-gray-500 tracking-widest uppercase mt-1">
            {title}
          </div>
        </div>
        <div aria-hidden="true">
          {icon}
        </div>
      </div>
    </div>
  );
};

export default HrCard;
