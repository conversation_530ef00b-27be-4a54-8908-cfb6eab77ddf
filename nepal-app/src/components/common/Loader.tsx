import React, { useEffect, useRef } from 'react';
import { useTranslation } from 'react-i18next';
import LoadingImage from '../../assets/images/loading4.gif';

interface LoaderProps {
  isLoading: boolean;
}

const Loader: React.FC<LoaderProps> = ({ isLoading }) => {
  const { t } = useTranslation();
  const loaderRef = useRef<HTMLDivElement>(null);

  // Move focus to loader when it appears
  useEffect(() => {
    if (isLoading) {
      loaderRef.current?.focus();
    }
  }, [isLoading]);

  if (!isLoading) return null;

  return (
    <div
      ref={loaderRef}
      className="fixed inset-0 z-[1000] bg-black/80 flex items-center justify-center outline-none"
      role="status"
      aria-live="assertive"
      // tabIndex={-1} // makes it focusable
    >
      <div className="flex flex-col items-center">
        <img src={LoadingImage} alt="" aria-hidden="true" />
        <span className="sr-only">{t('common.loading', 'Loading, please wait…')}</span>
      </div>
    </div>
  );
};

export default Loader;
