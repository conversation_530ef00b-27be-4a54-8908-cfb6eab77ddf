import React from 'react';
import Highcharts from 'highcharts';
import HighchartsReact from 'highcharts-react-official';
import type { SeriesPieOptions } from 'highcharts';
import exporting from 'highcharts/modules/exporting';


exporting(Highcharts);

const PieChart: React.FC = () => {
  const pieSeries: SeriesPieOptions = {
    name: 'Share',
    type: 'pie',
    data: [
      { name: 'Apples', y: 30 },
      { name: 'Bananas', y: 20 },
      { name: 'Cherries', y: 25 },
      { name: 'Grapes', y: 15 },
      { name: 'Oranges', y: 10 },
    ],
  };

  const options: Highcharts.Options = {
    chart: {
      type: 'pie',
    },
    title: {
      text: 'My Pie Chart',
    },
    tooltip: {
      pointFormat: '{series.name}: <b>{point.percentage:.1f}%</b>',
    },
    accessibility: {
      point: {
        valueSuffix: '%',
      },
    },
    plotOptions: {
      pie: {
        allowPointSelect: true,
        cursor: 'pointer',
        // exporting: {
        //   enabled: true
        // },
        dataLabels: {
          enabled: true,
          format: '<b>{point.name}</b>: {point.percentage:.1f} %',
        },
      },
    },
    series: [pieSeries],
  };

  return <HighchartsReact highcharts={Highcharts} options={options} />;
};

export default PieChart;
