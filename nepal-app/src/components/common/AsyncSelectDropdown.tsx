import React, { useEffect, useState } from 'react';
import Select from 'react-select';

type OptionType = {
  label: string;
  value: string | number;
  raw: any;
};

interface AsyncSelectDropdownProps<T> {
  label: string;
  fetchOptions: () => Promise<T[]>;
  getOptionLabel: (item: T) => string;
  getOptionValue: (item: T) => string | number;
  onChange: (value: T | null) => void;
  value: T | null;
}

export function AsyncSelectDropdown<T>({
  label,
  fetchOptions,
  getOptionLabel,
  getOptionValue,
  onChange,
  value,
}: AsyncSelectDropdownProps<T>) {
  const [options, setOptions] = useState<OptionType[]>([]);
  const [loading, setLoading] = useState<boolean>(true);

  useEffect(() => {
    let isMounted = true;
    setLoading(true);

    fetchOptions()
      .then((data) => {
        if (!isMounted) return;

        const mappedOptions = data.map((item) => ({
          label: getOptionLabel(item),
          value: getOptionValue(item),
          raw: item,
        }));
        setOptions(mappedOptions);
      })
      .catch((err) => {
        console.error(`Error fetching options for ${label}:`, err);
      })
      .finally(() => {
        if (isMounted) setLoading(false);
      });

    return () => {
      isMounted = false;
    };
  }, [fetchOptions]);

  const selectedOption =
    value != null
      ? options.find((opt) => opt.value === getOptionValue(value)) ?? null
      : null;

  return (
    <div className="w-full">
      <label className="sr-only block mb-1 font-medium text-gray-700">{label}</label>
      <Select
        isLoading={loading}
        options={options}
        value={selectedOption}
        onChange={(selected) => {
          onChange(selected ? (selected as OptionType).raw : null);
        }}
        placeholder={`Select ${label}`}
        isClearable
        className="text-sm"
      />
    </div>
  );
}
