import React, { useRef, useState } from 'react';
import StatusModal from './StatusModal';

interface FileUploadProps {
  onFileUpload: (file: File) => void;
  buttonText?: string;
  acceptedTypes?: string[];
}

const DEFAULT_ACCEPTED_TYPES = [
  'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', // .xlsx
  'text/csv',
  'application/vnd.ms-excel',
];

const FileUpload: React.FC<FileUploadProps> = ({
  onFileUpload,
  buttonText = 'Upload File',
  acceptedTypes = DEFAULT_ACCEPTED_TYPES,
}) => {
  const fileInputRef = useRef<HTMLInputElement>(null);
  const [error, setError] = useState<string | null>(null);

  const handleButtonClick = () => {
    setError(null);
    fileInputRef.current?.click();
  };

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (!file) return;

    const isValidType =
      acceptedTypes.includes(file.type) ||
      file.name.toLowerCase().endsWith('.xlsx') ||
      file.name.toLowerCase().endsWith('.csv');

    if (isValidType) {
      setError(null);
      onFileUpload(file);
    } else {
      setError('Please upload a valid Excel (.xlsx) or CSV (.csv) file.');
      // Reset the input value to allow re-uploading same file if needed
      if (fileInputRef.current) {
        fileInputRef.current.value = '';
      }
    }
  };

  return (
    <>
      <input
        ref={fileInputRef}
        type="file"
        accept=".xlsx, .csv"
        onChange={handleChange}
        className="hidden"
      />
      <button
        type="button"
        onClick={handleButtonClick}
        aria-label={buttonText}
        className="text-sm font-semibold text-blue-700 bg-blue-50 hover:bg-blue-100 py-2 px-4 rounded-full focus:outline-none focus-visible:ring focus-visible:ring-blue-300"
      >
        {buttonText}
      </button>
      {/* {error && <p className="text-red-600 mt-2 text-sm">{error}</p>} */}
    </>
  );
};

export default FileUpload;
