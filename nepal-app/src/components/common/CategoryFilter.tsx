import React, { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { listFacilityTypes } from '../../api/services/facility.service';

interface CategoryFilterProps {
  category: string;
  onCategoryChange: (category: string) => void;
  filter: string;
  onFilterChange: (filter: string) => void;
}

interface FacilityType {
  id: number;
  name: string;
  code: string;
  description: string | null;
}

const filterOptionsByCategory: Record<string, { id: string; label: string }[]> = {
  HR: [
    { id: 'doctor', label: 'Doctor' },
    { id: 'nurse', label: 'Nurse' },
    { id: 'chw', label: 'CHW' },
  ],
  // Facility will be dynamic
};

const CategoryFilter: React.FC<CategoryFilterProps> = ({
  category,
  onCategoryChange,
  filter,
  onFilterChange,
}) => {
  const { t } = useTranslation();
  const [facilityTypes, setFacilityTypes] = useState<FacilityType[]>([]);

  // Fetch facility types when category is Facility
  useEffect(() => {
    if (category === 'Facility') {
      const fetchFacilityTypes = async () => {
        try {
          const data = await listFacilityTypes();
          setFacilityTypes(data);
        } catch (error) {
          console.error('Failed to fetch facility types', error);
        }
      };
      fetchFacilityTypes();
    }
  }, [category]);

  // Auto-select first filter if current one is invalid
  useEffect(() => {
    const options =
      category === 'Facility'
        ? facilityTypes.map((f) => f.id.toString())
        : filterOptionsByCategory[category]?.map((f) => f.id) ?? [];

    if (category && (!filter || !options.includes(filter))) {
      onFilterChange(options[0] || '');
    }
  }, [category, filter, facilityTypes, onFilterChange]);

  return (
    <div className="flex justify-between items-start gap-10" aria-label={t('categoryFilter.ariaLabel')}>
      {/* Category Dropdown */}
      <div className="flex flex-col gap-1">
        <label htmlFor="category" className="text-sm font-medium">
          {t('categoryFilter.category') || 'Category'}
        </label>
        <select
          id="category"
          value={category}
          onChange={(e) => onCategoryChange(e.target.value)}
          aria-describedby="category-desc"
          className="appearance-none px-4 py-2 w-130 text-sm rounded-lg border border-[#edeef0] bg-[#f8fafb] font-normal text-[#0e2238] transition-all focus:border-[#00bac7] focus:outline-none focus:ring-1 focus:ring-[#00bac7]"
        >
          <option value="">{t('categoryFilter.selectCategory') || 'Select a category'}</option>
          <option value="HR">{t('categoryFilter.categories.hr') || 'HR'}</option>
          <option value="Facility">{t('categoryFilter.categories.facility') || 'Facility'}</option>
        </select>
        <span id="category-desc" className="sr-only">
          {t('categoryFilter.categoryDescription')}
        </span>
      </div>

      {/* Filter Dropdown */}
      <div className="flex flex-col gap-1">
        <label htmlFor="filter" className="text-sm font-medium">
          {t('categoryFilter.filter') || 'Filter'}
        </label>
        <select
          id="filter"
          value={filter}
          onChange={(e) => onFilterChange(e.target.value)}
          aria-describedby="filter-desc"
          className="appearance-none px-4 py-2 w-130 text-sm rounded-lg border border-[#edeef0] bg-[#f8fafb] font-normal text-[#0e2238] transition-all focus:border-[#00bac7] focus:outline-none focus:ring-1 focus:ring-[#00bac7]"
          disabled={!category}
          aria-disabled={!category}
        >
          <option value="">{t('categoryFilter.selectFilter') || 'Select a filter'}</option>

          {category === 'Facility' &&
            facilityTypes.map((facility) => (
              <option key={facility.id} value={facility.id}>
                {facility.name}
              </option>
            ))}

          {category !== 'Facility' &&
            filterOptionsByCategory[category]?.map(({ id, label }) => (
              <option key={id} value={id}>
                {t(`categoryFilter.filters.${id}`) || label}
              </option>
            ))}
        </select>
        <span id="filter-desc" className="sr-only">
          {t('categoryFilter.filterDescription')}
        </span>
      </div>
    </div>
  );
};

export default CategoryFilter;
