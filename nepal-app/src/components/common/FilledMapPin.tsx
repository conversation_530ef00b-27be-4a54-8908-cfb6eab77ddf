import React from 'react';
import { MapPin } from 'lucide-react';

interface FilledMapPinProps {
  size?: number;
  color?: string;
  dotColor?: string;
  dotSize?: number;
}

const FilledMapPin: React.FC<FilledMapPinProps> = ({
  size = 32,
  color = '#ef4444',
  dotColor = '#ffffff',
  dotSize = 8,
}) => {
  return (
    <div
      className="relative"
      style={{ width: size, height: size }}
      aria-hidden="true"
    >
      <MapPin
        className="w-full h-full"
        style={{ color, fill: color }}
        aria-hidden="true"
      />
      <div
        className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 rounded-full"
        style={{
          width: dotSize,
          height: dotSize,
          backgroundColor: dotColor,
        }}
      />
    </div>
  );
};

export default FilledMapPin;
