import React, { useState } from 'react';
import { useAuth } from '../../context/AuthContext';
import { useProfile } from '../../context/ProfileContext'; // import your profile hook
import { useNavigate } from 'react-router-dom';
import userImage from '../../assets/images/user.png';
import { useTranslation } from 'react-i18next';
import { setGlobalLoading } from '../../api/loaderHookBridge';

const UserMenu: React.FC<{ className?: string }> = ({ className = '' }) => {
  const { logout } = useAuth();
  const { profile } = useProfile(); // get profile data here
  const navigate = useNavigate();
  const [isSigningOut, setIsSigningOut] = useState(false);
  const { t } = useTranslation();

  if (!profile) return null; // wait for profile

  const handleChangePassword = () => {
    navigate('/change-password');
  };

  const handleSignOut = async () => {
    setIsSigningOut(true);
    try {
      setGlobalLoading(true);
      await logout();
      navigate('/login');
    } finally {
      setGlobalLoading(false);
      setIsSigningOut(false);
    }
  };

  return (
    <div
      className={`bg-white border border-gray-200 rounded-lg shadow-lg min-w-[200px] transition-all ${className}`}
      role="menu"
      aria-label={t('common.userMenu')}
    >
      <div className="p-4 border-b border-gray-200">
        <div className="flex items-center gap-3">
          <img
            src={userImage}
            alt={t('common.userAvatar')}
            className="w-10 h-10 rounded-full object-cover"
          />
          <div>
            <h4 className="text-base font-semibold text-gray-800 m-0">
              {profile.full_name}
            </h4>
            <p className="text-sm text-gray-500 m-0">{profile.email}</p>
          </div>
        </div>
      </div>

      <button
        onClick={handleChangePassword}
        className="w-full text-left px-4 py-3 text-gray-700 hover:bg-gray-50 transition"
        role="menuitem"
      >
        {t('common.changePassword')}
      </button>

      <hr className="my-2 border-gray-200" />

      <button
        onClick={handleSignOut}
        className="w-full text-left px-4 py-3 text-gray-700 hover:bg-gray-50 transition"
        role="menuitem"
        disabled={isSigningOut}
      >
        {isSigningOut ? t('common.signingOut') : t('common.signOut')}
      </button>
    </div>
  );
};

export default UserMenu;
