import { useState } from "react";
import { CircleArrowDown } from "lucide-react";

interface HmisCardProps {
  title: string;
  children?: React.ReactNode;
  total?: number;
}

function HmisCard({ title, children, total = 0 }: HmisCardProps) {
  const [isOpen, setIsOpen] = useState(false);

  return (
    <div className="overflow-hidden transition-all p-4 border border-gray-200 rounded-[2%] bg-[#fafafa]">
      <div className={`w-full flex items-center justify-between px-5 py-4 hover:bg-gray-50 transition-colors border-b pb-2 mb-2 ${isOpen ? 'border-b-2 border-gray-800' : 'border-b border-gray-300'}`}>
        <div className="text-left">
          <h3 className="text-sm font-bold text-gray-900">{title}</h3>
        </div>

        <div className="flex gap-2 items-center">
          <span className="text-xs text-gray-500 mt-1">Total: {total}</span>
          <button
            onClick={() => setIsOpen(!isOpen)}
            aria-expanded={isOpen}
            aria-controls={`hmis-card-content-${title.replace(/\s+/g, '-')}`}
            className="p-1 rounded-full focus:outline-none focus-visible:ring-2 focus-visible:ring-offset-2 focus-visible:ring-blue-500"
          >
            <CircleArrowDown
              className={`h-5 w-5 text-gray-500 transition-transform duration-200 ${isOpen ? 'rotate-180' : ''}`}
              aria-hidden="true"
            />
          </button>
        </div>
      </div>

      {isOpen && children && (
        <div id={`hmis-card-content-${title.replace(/\s+/g, '-')}`} className="px-4 pb-5 text-sm text-gray-700">
          {children}
        </div>
      )}
    </div>
  );
}

export default HmisCard;
