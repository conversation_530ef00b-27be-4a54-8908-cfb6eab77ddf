import React from 'react';
import { useProfile } from '../../context/ProfileContext';
import { useLocation } from '../../context/LocationContext';
import { getDecodedToken } from '../../utils/token';

const AuthorityInfo: React.FC = () => {
  const decoded = getDecodedToken();

  if (!decoded) return null;

  const authorityLevel = decoded.authority_level.charAt(0).toUpperCase() + decoded.authority_level.slice(1);
  const locationName = decoded.authority_location_name ?? '';

  return (
    <div className="p-2 rounded-md bg-gray-50 shadow-sm text-gray-800 text-sm">
      <span className="font-semibold">{authorityLevel}</span>: {locationName}
    </div>
  );
};

export default AuthorityInfo;

