import React from 'react';
import { useNavigate } from 'react-router-dom';

interface StatCardProps {
  label: string;
  value: string | number;
  icon?: React.ReactNode;
  note?: string;
  borderPosition?: 'left' | 'top' | 'none';
  filter?: string;
}

const StatCard: React.FC<StatCardProps> = ({
  label,
  value,
  icon,
  note,
  borderPosition = 'left',
  filter,
}) => {
  const navigate = useNavigate();

  const handleClick = () => {
    if (filter) {
      navigate(`/report?category=Geography&filter=${filter}`);
    }
  };

  const borderClasses =
    borderPosition === 'top'
      ? 'border-t-0'
      : borderPosition === 'left'
      ? 'border-l-0'
      : '';

  const valueId = `stat-value-${label.replace(/\s+/g, '-').toLowerCase()}`;

  return (
    <section
      className={`border border-gray-300 p-4 bg-white flex flex-col justify-between ${borderClasses}`}
      role="region"
      aria-labelledby={`stat-${label.replace(/\s+/g, '-').toLowerCase()}`}
    >
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-3">
          {icon && <span aria-hidden="true">{icon}</span>}
          <h3
            id={`stat-${label.replace(/\s+/g, '-').toLowerCase()}`}
            className="text-sm text-gray-700 font-medium"
          >
            {label}
          </h3>
        </div>

        <button
          onClick={handleClick}
          className="text-base font-bold text-red-500 underline hover:text-red-700 focus:outline-none focus:ring-2 focus:ring-red-300 rounded"
          aria-label={`View details for ${label}`}
          id={valueId}
        >
          {value}
        </button>
      </div>

      {note && (
        <p className="text-xs text-gray-600 mt-1" aria-describedby={valueId}>
          {note}
        </p>
      )}
    </section>
  );
};

export default StatCard;
