import React from 'react';
import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router-dom';

interface InfoCardProps {
  title: string;
  filter: string;
  count: number;
  icon: React.ReactNode;
  onViewList?: (filter: string) => void;
  countColor?: string;
  bottomTitleColor?: string;
  bottomTitleBgColor?: string;
}

const InfoCard: React.FC<InfoCardProps> = ({
  title,
  filter,
  count,
  icon,
  onViewList,
  countColor,
  bottomTitleColor,
  bottomTitleBgColor,
}) => {
  const { t } = useTranslation();
  const navigate = useNavigate();

  const handleNavigate = () => {
    navigate(`/report?category=Geography&filter=${filter}`);
  };

  return (
    <div
      role="button"
      tabIndex={0}
      aria-label={`${title} card, press Enter or Space to view details`}
      onClick={handleNavigate}
      onKeyDown={(e) => {
        if (e.key === 'Enter' || e.key === ' ') {
          e.preventDefault();
          handleNavigate();
        }
      }}
      className="relative w-full rounded-2xl shadow hover:shadow-lg transition duration-200 bg-white cursor-pointer hover:scale-[1.01] focus:outline-none focus:ring-2 focus:ring-blue-500"
    >
      <div className="flex justify-between items-start px-6 py-3 mb-4">
        <div>
          <div className={`text-2xl leading-tight ${countColor || 'text-gray-900'}`}>{count}</div>
          <div className="text-[12.6px] text-gray-500 tracking-widest uppercase mt-1">{title}</div>
        </div>
        <div aria-hidden="true">{icon}</div>
      </div>

      <div
        className={`text-normal px-6 py-3 font-normal rounded-b-2xl rounded-t-none
          ${bottomTitleColor || 'text-gray-500'} 
          ${bottomTitleBgColor || ''}`}
      >
        {title}
      </div>
    </div>
  );
};

export default InfoCard;
