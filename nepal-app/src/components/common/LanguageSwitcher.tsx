import React, { useState, useRef, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { useLanguageContext } from '../../context/LanguageContext';

const LanguageSwitcher: React.FC = () => {
  const { t } = useTranslation();
  const { language, changeLanguage } = useLanguageContext();
  const [isOpen, setIsOpen] = useState(false);
  const [focusedIndex, setFocusedIndex] = useState(0);
  const buttonRef = useRef<HTMLButtonElement>(null);
  const itemRefs = useRef<(HTMLButtonElement | null)[]>([]);

  const languages = [
    { code: 'en', label: 'English', flag: '🇬🇧' },
    { code: 'ne', label: 'नेपाली', flag: '🇳🇵' },
  ];

  const toggleMenu = () => setIsOpen(prev => !prev);

  const handleLanguageChange = (lang: 'en' | 'ne') => {
    changeLanguage(lang);
    setIsOpen(false);
    buttonRef.current?.focus();
  };

  const handleMenuKeyDown = (e: React.KeyboardEvent<HTMLDivElement>) => {
    if (!isOpen) return;

    switch (e.key) {
      case 'Escape':
        e.preventDefault();
        setIsOpen(false);
        buttonRef.current?.focus();
        break;
      case 'ArrowDown':
        e.preventDefault();
        setFocusedIndex((prev) => (prev + 1) % languages.length);
        break;
      case 'ArrowUp':
        e.preventDefault();
        setFocusedIndex((prev) => (prev - 1 + languages.length) % languages.length);
        break;
    }
  };

  // Focus the current menu item when menu opens or index changes
  useEffect(() => {
    if (isOpen) {
      itemRefs.current[focusedIndex]?.focus();
    }
  }, [isOpen, focusedIndex]);

  return (
    <div className="relative">
      <button
        ref={buttonRef}
        onClick={toggleMenu}
        className="flex items-center space-x-2 px-3 py-2 rounded-lg bg-white shadow-sm hover:bg-gray-50 transition-colors"
        aria-haspopup="menu"
        aria-expanded={isOpen}
        aria-controls="language-menu"
        aria-label={t('common.languageSwitcher')}
        id="language-button"
      >
        <span className="text-xl" aria-hidden="true">
          {languages.find(l => l.code === language)?.flag}
        </span>
        <span className="text-sm font-medium text-gray-700">
          {languages.find(l => l.code === language)?.label}
        </span>
      </button>

      {isOpen && (
        <>
          <div className="fixed inset-0 z-40" onClick={() => setIsOpen(false)} />
          <div
            id="language-menu"
            role="menu"
            aria-label={t('common.languageMenuLabel')}
            aria-orientation="vertical"
            className="absolute right-0 mt-2 w-48 rounded-md shadow-lg bg-white ring-1 ring-gray-300 z-50"
            onKeyDown={handleMenuKeyDown}
          >
            {languages.map((lang, index) => (
              <button
                key={lang.code}
                ref={(el) => {
                  itemRefs.current[index] = el;
                }}                
                onClick={() => handleLanguageChange(lang.code as 'en' | 'ne')}
                className={`flex items-center w-full px-4 py-2 text-sm ${
                  language === lang.code
                    ? 'bg-gray-100 text-gray-900'
                    : 'text-gray-700 hover:bg-gray-50'
                  } focus-visible:outline-2 focus-visible:outline-blue-500 focus-visible:rounded-md
                  ${index === 0 ? 'rounded-t-md' : ''}  
                  ${index === 1 ? 'rounded-b-md' : ''}`}
                role="menuitem"
              >
                <span className="text-xl mr-2" aria-hidden="true">{lang.flag}</span>
                <span>{lang.label}</span>
              </button>
            ))}
          </div>
        </>
      )}
    </div>
  );
};

export default LanguageSwitcher;
