import React, { useState, useMemo, useEffect } from 'react';
// import type { Column } from './Table';
import { useTranslation } from 'react-i18next';


type PaginatedTableProps<T> = {
  data: T[];
  columns: Column<T>[];
  title?: string;
  className?: string;
  getRowKey?: (row: T, index: number) => string;
};

export type Column<T> = {
  header: string;
  accessor: keyof T | string;
  render?: (value: any, row: T, index: number) => React.ReactNode;
};


export function PaginatedTable<T extends Record<string, any>>({
  data,
  columns,
  title,
  getRowKey,
  className = '',
}: PaginatedTableProps<T>) {
  const { t } = useTranslation();
  const [currentPage, setCurrentPage] = useState(1);
  const [entriesPerPage, setEntriesPerPage] = useState(10);
  const [searchTerm, setSearchTerm] = useState('');

  const filteredData = useMemo(() => {
    if (!searchTerm) return data;
    const lower = searchTerm.toLowerCase();
    return data.filter(row =>
      columns.some(col => {
        const value = row[col.accessor];
        return value && String(value).toLowerCase().includes(lower);
      })
    );
  }, [data, searchTerm, columns]);

  const totalPages = Math.ceil(filteredData.length / entriesPerPage);
  const indexOfLastEntry = currentPage * entriesPerPage;
  const indexOfFirstEntry = indexOfLastEntry - entriesPerPage;
  const currentEntries = filteredData.slice(indexOfFirstEntry, indexOfLastEntry);

  useEffect(() => {
    if (currentPage > totalPages) {
      setCurrentPage(1);
    }
  }, [currentPage, totalPages]);

  return (
    <div className={`bg-white p-4 ${className} text-sm`}>
      {title && (
        <div className="mb-4 text-gray-700 font-semibold uppercase">{title}</div>
      )}

      <div className="flex justify-between items-center mb-4 text-gray-700">
        <div className="flex items-center gap-2">
          <label htmlFor="entriesSelect">{t('common.show')}</label>
          <select
            id="entriesSelect"
            className="border border-gray-300 rounded px-2 py-1 text-sm"
            value={entriesPerPage}
            onChange={(e) => {
              setEntriesPerPage(Number(e.target.value));
              setCurrentPage(1);
            }}
          >
            {[10, 25, 50, 100].map(num => (
              <option key={num} value={num}>{num}</option>
            ))}
          </select>
          <span>{t('common.entries')}</span>
        </div>

        <div className="flex items-center gap-2">
          <label htmlFor="searchInput">{t('common.find')}</label>
          <input
            id="searchInput"
            type="text"
            className="border border-gray-300 rounded px-2 py-1 w-48 text-sm"
            value={searchTerm}
            onChange={(e) => {
              setSearchTerm(e.target.value);
              setCurrentPage(1);
            }}
          />
        </div>
      </div>

      <div className="overflow-auto">
        <table role="table" className="min-w-full bg-white text-left text-sm">
          {title && <caption className="sr-only">{title}</caption>}
          <thead className="text-gray-400 font-semibold">
            <tr>
              {columns.map(col => (
                <th
                  key={String(col.accessor)}
                  scope="col"
                  className="px-4 py-3"
                >
                  {col.header}
                </th>
              ))}
            </tr>
          </thead>
          <tbody>
            {currentEntries.length === 0 ? (
              <tr>
                <td colSpan={columns.length} className="px-4 py-3 text-center">
                  {t('common.noDataAvailable')}
                </td>
              </tr>
            ) : (
              currentEntries.map((row, idx) => (
                <tr key={idx} className="hover:bg-gray-50">
                  {columns.map(col => (
                    <td key={String(col.accessor)} className="px-4 py-3">
                      {col.render
                        ? col.render(row[col.accessor], row, idx)
                        : String(row[col.accessor] ?? '-')}
                    </td>
                  ))}
                </tr>
              ))
            )}
          </tbody>
        </table>
      </div>

      <div
        className="mt-4 text-gray-600 text-sm"
        aria-live="polite"
        aria-atomic="true"
      >
        {t('common.showing', {
          from: filteredData.length === 0 ? 0 : indexOfFirstEntry + 1,
          to: Math.min(indexOfLastEntry, filteredData.length),
          total: filteredData.length,
        })}
      </div>

      <div className="flex flex-wrap items-center gap-2 mt-4">
        <button
          className="px-3 py-1 border border-gray-300 rounded disabled:opacity-50 text-sm"
          onClick={() => setCurrentPage((prev) => prev - 1)}
          disabled={currentPage === 1}
        >
          {t('common.previous')}
        </button>

        {(() => {
          const pageButtons: (number | 'ellipsis')[] = [];
          const range = 2;

          // Always include first page
          pageButtons.push(1);

          const start = Math.max(2, currentPage - range);
          const end = Math.min(totalPages - 1, currentPage + range);

          // Insert left ellipsis if needed
          if (start > 2) {
            pageButtons.push('ellipsis');
          }

          // Pages between start and end
          for (let i = start; i <= end; i++) {
            pageButtons.push(i);
          }

          // Insert right ellipsis if needed
          if (end < totalPages - 1) {
            pageButtons.push('ellipsis');
          }

          // Always include last page (if more than 1)
          if (totalPages > 1) {
            pageButtons.push(totalPages);
          }

          return pageButtons.map((page, idx) => {
            if (page === 'ellipsis') {
              return (
                <span key={`ellipsis-${idx}`} className="px-2 text-gray-500" aria-hidden="true">
                  ...
                </span>
              );
            }

            return (
              <button
                key={page}
                onClick={() => setCurrentPage(page)}
                className={`px-3 py-1 border rounded text-sm ${
                  currentPage === page
                    ? 'bg-blue-500 text-white border-blue-500'
                    : 'border-gray-300 text-gray-700 hover:bg-gray-100'
                }`}
                aria-current={currentPage === page ? 'page' : undefined}
              >
                {page}
              </button>
            );
          });
        })()}


        <button
          className="px-3 py-1 border border-gray-300 rounded disabled:opacity-50 text-sm"
          onClick={() => setCurrentPage((prev) => prev + 1)}
          disabled={currentPage === totalPages || totalPages === 0}
        >
          {t('common.next')}
        </button>
      </div>
    </div>
  );
}
