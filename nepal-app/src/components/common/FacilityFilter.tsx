import React from 'react';
import { AsyncSelectDropdown } from './AsyncSelectDropdown';
import {
  listAuthorityLevels,
  listFacilityLevels,
  listFacilityTypes,
} from '../../api/services/facility.service';

type FacilityOption = {
  id: number;
  name: string;
  code: string;
};

interface FacilityFilterProps {
  facilityType?: number;
  setFacilityType: (id: number | undefined) => void;
  facilityLevel?: number;
  setFacilityLevel: (id: number | undefined) => void;
  authority?: number;
  setAuthority: (id: number | undefined) => void;
}

const getLabel = (item: FacilityOption) => item.name;
const getValue = (item: FacilityOption) => item.id;

const FacilityFilter: React.FC<FacilityFilterProps> = ({
  facilityType,
  setFacilityType,
  facilityLevel,
  setFacilityLevel,
  authority,
  setAuthority,
}) => {
  return (
    <div className="flex flex-col md:flex-row gap-4 mt-4 mb-4">
      <AsyncSelectDropdown
        label="Facility Type"
        fetchOptions={listFacilityTypes}
        getOptionLabel={getLabel}
        getOptionValue={getValue}
        onChange={(selected) => setFacilityType(selected?.id)}
        value={
          facilityType !== undefined
            ? { id: facilityType, name: '', code: '' }
            : null
        }
      />
      <AsyncSelectDropdown
        label="Facility Level"
        fetchOptions={listFacilityLevels}
        getOptionLabel={getLabel}
        getOptionValue={getValue}
        onChange={(selected) => setFacilityLevel(selected?.id)}
        value={
          facilityLevel !== undefined
            ? { id: facilityLevel, name: '', code: '' }
            : null
        }
      />
      <AsyncSelectDropdown
        label="Authority Level"
        fetchOptions={listAuthorityLevels}
        getOptionLabel={getLabel}
        getOptionValue={getValue}
        onChange={(selected) => setAuthority(selected?.id)}
        value={
          authority !== undefined
            ? { id: authority, name: '', code: '' }
            : null
        }
      />
    </div>
  );
};

export default FacilityFilter;
