import React from 'react';
import { useTranslation } from 'react-i18next';
import type { Column } from './Table';

type PaginatedTableProps<T> = {
  data: T[];
  columns: Column<T>[];
  page: number;
  pageSize: number;
  total: number;
  onPageChange: (newPage: number) => void;
  onPageSizeChange?: (newSize: number) => void;
  onSearchChange?: (term: string) => void;
  title?: string;
  className?: string;
  getRowKey?: (row: T, index: number) => string;
};

export function PaginatedTablee<T extends Record<string, any>>({
  data,
  columns,
  page,
  pageSize,
  total,
  onPageChange,
  onPageSizeChange,
  onSearchChange,
  title,
  className = '',
  getRowKey,
}: PaginatedTableProps<T>) {
  const { t } = useTranslation();

  const totalPages = Math.ceil(total / pageSize);

  const renderPaginationButtons = () => {
    const pageButtons: (number | 'ellipsis')[] = [];
    const delta = 2; // Show current page ±2
  
    const start = Math.max(2, page - delta);
    const end = Math.min(totalPages - 1, page + delta);
  
    // Always show first page
    pageButtons.push(1);
  
    // Ellipsis between first page and start range
    if (start > 2) {
      pageButtons.push('ellipsis');
    }
  
    // Pages around current page
    for (let i = start; i <= end; i++) {
      pageButtons.push(i);
    }
  
    // Ellipsis between end range and last page
    if (end < totalPages - 1) {
      pageButtons.push('ellipsis');
    }
  
    // Always show last page if it's not the first
    if (totalPages > 1) {
      pageButtons.push(totalPages);
    }
  
    return pageButtons.map((p, idx) =>
      p === 'ellipsis' ? (
        <span key={`ellipsis-${idx}`} className="px-2 text-gray-500" aria-hidden="true">
          ...
        </span>
      ) : (
        <button
          key={p}
          onClick={() => onPageChange(p)}
          className={`px-3 py-1 border rounded text-sm ${
            page === p
              ? 'bg-blue-500 text-white border-blue-500'
              : 'border-gray-300 text-gray-700 hover:bg-gray-100'
          }`}
          aria-current={page === p ? 'page' : undefined}
        >
          {p}
        </button>
      )
    );
  };  

  return (
    <div className={`bg-white p-4 shadow ${className} text-sm`}>
      {title && (
        <div className="mb-4 text-gray-700 font-semibold uppercase">
          {title}
        </div>
      )}

      <div className="flex justify-between items-center mb-4 text-gray-700">
        {onPageSizeChange && (
          <div className="flex items-center gap-2">
            <label htmlFor="entriesSelect">{t('common.show')}</label>
            <select
              id="entriesSelect"
              className="border border-gray-300 rounded px-2 py-1 text-sm"
              value={pageSize}
              onChange={(e) => {
                const size = Number(e.target.value);
                onPageSizeChange(size);
                onPageChange(1); // Reset to page 1
              }}
            >
              {[10, 25, 50, 100].map((n) => (
                <option key={n} value={n}>
                  {n}
                </option>
              ))}
            </select>
            <span>{t('common.entries')}</span>
          </div>
        )}

        {onSearchChange && (
          <div className="flex items-center gap-2">
            <label htmlFor="searchInput">{t('common.find')}</label>
            <input
              id="searchInput"
              type="text"
              className="border border-gray-300 rounded px-2 py-1 w-48 text-sm"
              onChange={(e) => {
                onSearchChange(e.target.value);
                onPageChange(1); // Reset to page 1
              }}
            />
          </div>
        )}
      </div>

      <div className="overflow-auto">
        <table className="min-w-full bg-white text-left text-sm">
          {title && <caption className="sr-only">{title}</caption>}
          <thead className="text-gray-400 font-semibold">
            <tr>
              {columns.map((col) => (
                <th key={String(col.accessor)} className="px-4 py-3">
                  {col.header}
                </th>
              ))}
            </tr>
          </thead>
          <tbody>
            {data.length === 0 ? (
              <tr>
                <td colSpan={columns.length} className="px-4 py-3 text-center">
                  {t('common.noDataAvailable')}
                </td>
              </tr>
            ) : (
              data.map((row, index) => (
                <tr key={getRowKey ? getRowKey(row, index) : index} className="hover:bg-gray-50">
                  {columns.map((col) => (
                    <td key={String(col.accessor)} className="px-4 py-3">
                      {col.render
                        ? col.render(row[col.accessor], row, index)
                        : String(row[col.accessor] ?? '-')}
                    </td>
                  ))}
                </tr>
              ))
            )}
          </tbody>
        </table>
      </div>

      <div className="mt-4 text-gray-600 text-sm">
        {t('common.showing', {
          from: total === 0 ? 0 : (page - 1) * pageSize + 1,
          to: Math.min(page * pageSize, total),
          total: total,
        })}
      </div>

      <div className="flex flex-wrap items-center gap-2 mt-4">
        <button
          className="px-3 py-1 border border-gray-300 rounded disabled:opacity-50 text-sm"
          onClick={() => onPageChange(page - 1)}
          disabled={page === 1}
        >
          {t('common.previous')}
        </button>

        {renderPaginationButtons()}

        <button
          className="px-3 py-1 border border-gray-300 rounded disabled:opacity-50 text-sm"
          onClick={() => onPageChange(page + 1)}
          disabled={page === totalPages || totalPages === 0}
        >
          {t('common.next')}
        </button>
      </div>
    </div>
  );
}
