import React, { useEffect, useState } from 'react';
import Highcharts from 'highcharts/highmaps';
import HighchartsReact from 'highcharts-react-official';
import 'highcharts/modules/exporting';
import 'highcharts/modules/accessibility';
import { useNavigate } from 'react-router-dom';
import booleanPointInPolygon from '@turf/boolean-point-in-polygon';
import { point as turfPoint } from '@turf/helpers';

interface GeoMapProps {
  level: 'country' | 'province' | 'district' | 'municipality' | 'ward';
  allowedRegionId?: number | null;
  geoJsonOutline: any;
  geoJsonDivision: any;
  facilities: Array<{
    Facility_ID: number | string;
    name?: string;
    Province_ID: number;
    District_ID: number;
    Palika_ID: number;
    Ward_ID: number;
    Facility_Type: number;
    Facility_Name: string;
    Facility_Code: string;
    latitude: number;
    longitude: number;
  }>;
  filterType?: number;
  onRegionClick?: (regionCode: string) => void;
  onMarkerClick?: (facility: any) => void;
}

const GeoMap: React.FC<GeoMapProps> = ({
  level,
  allowedRegionId,
  geoJsonOutline,
  geoJsonDivision,
  facilities,
  filterType,
  onRegionClick,
  onMarkerClick
}) => {
  const [mapOptions, setMapOptions] = useState<Highcharts.Options>({});
  const [loading, setLoading] = useState(true);
  const navigate = useNavigate();

  useEffect(() => {
    if (!geoJsonOutline || !geoJsonDivision) return;

    const filteredDivision = {
      ...geoJsonDivision,
      features: geoJsonDivision.features.filter((f: any) => {
        if (!allowedRegionId) return true;
      
        switch (level) {
          case 'province':
            return f.properties?.PROVINCE === allowedRegionId;
          case 'district':
            return f.properties?.dID === allowedRegionId;
          default:
            return true;
        }
      }),      
    };
    
    const filteredFacilities = facilities;

    const regionCounts: Record<string, number> = {};
    filteredFacilities.forEach(fac => {
      let regionCode = '';
      switch (level) {
        case 'province':
          regionCode = fac.Province_ID?.toString();
          break;
        case 'district':
          regionCode = fac.District_ID?.toString();
          break;
        case 'municipality':
          regionCode = fac.Palika_ID?.toString();
          break;
        default:
          regionCode = fac.Province_ID?.toString();
      }
      if (regionCode) {
        regionCounts[regionCode] = (regionCounts[regionCode] || 0) + 1;
      }
    });

    const regionData = filteredDivision.features.map((feature: any) => {
      const geoProp = (() => {
        switch (level) {
          case 'province': return 'PROVINCE';
          case 'district': return 'DISTRICT';
          case 'municipality': return 'PALIKA';
          default: return 'PROVINCE';
        }
      })();
      
      const regionId = feature.properties?.[geoProp]?.toString();
      console.log('RegionCounts:', regionCounts);
      console.log('Feature properties:', feature.properties);


      const hcKey = feature.properties?.['hc-key'];
      return {
        'hc-key': hcKey,
        value: regionId ? regionCounts[regionId] || 0 : 0,
      };
          });

    const markerSeries = filteredFacilities.map(f => ({
      name: f.name,
      lat: f.latitude,
      lon: f.longitude,
      custom: f
    }));

    const options: Highcharts.Options = {
      chart: {
        map: filteredDivision,
        height: 450,
        backgroundColor: 'transparent',
        style: { fontFamily: 'Inter, sans-serif' },
        animation: false
      },
      title: { text: undefined },
      mapNavigation: {
        enabled: true,
        enableMouseWheelZoom: false
      },
      colorAxis: {
        min: 0,
        max: Math.max(...Object.values(regionCounts), 10),
        stops: [
          [0, '#e0f2fe'],
          [0.5, '#60a5fa'],
          [1, '#1d4ed8']
        ]
      },
      tooltip: {
        useHTML: false,
        outside: true,
        formatter: function () {
          const point = this as any;
          if (point.lat && point.lon) {
            return `<strong>${point.name}</strong>`;
          }
          const provinceName = point?.properties?.PROVINCE_EN || point.name || 'Unknown';
          const count = point.value ?? 0;
          return `${provinceName}<br/>Facilities: ${count}`;
        }
      },
      plotOptions: {
        map: {
          states: {
            hover: {
              color: '#06b6d4'
            },
            inactive: {
              opacity: 1
            }
          },
          point: {
            events: {
              click: function (this: Highcharts.Point) {
                const regionCode = (this as any)['hc-key'];
                if (onRegionClick) onRegionClick(regionCode);
              }
            }
          }
        }
      },
      exporting: {
        enabled: true
      },
      series: [
        {
          type: 'map',
          name: 'Health Facilities Count',
          mapData: filteredDivision,
          data: regionData,
          borderColor: '#ccc',
          borderWidth: 1,
          nullColor: '#f9fafb',
          showInLegend: true,
          animation: false
        },
        {
          type: 'mappoint',
          name: `Facilities`,
          boostThreshold: 1,
          color: '#ef4444',
          data: markerSeries,
          marker: {
            symbol: 'url(/custom-marker.svg)',
            width: 24,
            height: 24
          },
          dataLabels: {
            enabled: false // <- Disable name labels on the map
          },
          tooltip: {
            pointFormat: '<strong>{point.name}</strong>'
          },
          point: {
            events: {
              click: function (this: Highcharts.Point) {
                const p = this as any;
                if (onMarkerClick) onMarkerClick(p.custom);

                // Navigate to facility detail page
                if (p.custom?.Facility_ID) {
                  navigate(`/facilities/${p.custom.Facility_ID}`);
                }
              }
            }
          }
        },
        {
          type: 'mapline',
          name: 'Border',
          mapData: geoJsonOutline,
          color: '#1f2937',
          showInLegend: false,
          enableMouseTracking: true
        }
      ],
      credits: { enabled: false }
    };

    setMapOptions(options);
    setLoading(false);
  }, [geoJsonOutline, geoJsonDivision, facilities, filterType, level, allowedRegionId]);

  if (loading) {
    return (
      <div className="flex items-center justify-center h-96">
        <div className="text-gray-600">Loading map...</div>
      </div>
    );
  }

  return (
    <section
      aria-label="Geographic distribution map of health facilities"
      className="relative mt-10"
    >
      <HighchartsReact
        highcharts={Highcharts}
        constructorType="mapChart"
        options={mapOptions}
      />
    </section>
  );  
};

export default GeoMap;
