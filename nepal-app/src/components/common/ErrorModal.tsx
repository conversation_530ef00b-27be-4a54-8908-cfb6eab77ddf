import React, { useEffect, useRef } from 'react';
import { FocusTrap } from 'focus-trap-react';
import { CircleX } from 'lucide-react';

interface ErrorModalProps {
  isOpen?: boolean;
  onClose?: () => void;
  onOk?: () => void;
  message?: string;
}

const ErrorModal: React.FC<ErrorModalProps> = ({
  isOpen = true,
  onClose,
  onOk,
  message = "An unexpected error occurred."
}) => {
  const okButtonRef = useRef<HTMLButtonElement>(null);

  // Focus the OK button when modal opens
  useEffect(() => {
    if (isOpen) {
      okButtonRef.current?.focus();
    }
  }, [isOpen]);

  // Close modal on ESC key
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.key === 'Escape') {
        onClose?.();
      }
    };
    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, [onClose]);

  if (!isOpen) return null;

  return (
    <div
      className="fixed inset-0 bg-black/50 flex items-center justify-center z-[1000]"
      role="alertdialog"
      aria-modal="true"
      aria-labelledby="error-modal-title"
      aria-describedby="error-modal-description"
    >
      <FocusTrap>
        <div
          className="bg-white text-center rounded-md shadow-xl p-6 w-[478px] max-w-[90vw] mx-4 animate-alert outline-none"
          tabIndex={-1}
        >
          {/* Hidden Close Button for accessibility (Escape key also works) */}
          <button
            onClick={onClose}
            aria-label="Close error dialog"
            className="sr-only"
          >
            Close
          </button>

          {/* Error Icon */}
          <div className="flex justify-center my-6">
            <CircleX className="w-20 h-20 text-red-400" />
          </div>

          {/* Message */}
          <div className="mb-8">
            <h2 id="error-modal-title" className="sr-only">
              Error
            </h2>
            <p
              id="error-modal-description"
              className="text-gray-500 text-base font-light"
            >
              {message}
            </p>
          </div>

          {/* Button */}
          <div className="flex justify-center">
            <button
              ref={okButtonRef}
              onClick={onOk}
              className="bg-sky-300 hover:bg-blue-500 focus:outline-none focus:ring-2 focus:ring-blue-300 text-white font-medium px-8 py-3 rounded-md text-base"
            >
              OK
            </button>
          </div>
        </div>
      </FocusTrap>
    </div>
  );
};

export default ErrorModal;
