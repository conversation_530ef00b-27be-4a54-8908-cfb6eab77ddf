import React, { useState, forwardRef } from 'react';
import type { InputHTMLAttributes } from 'react';
import { Eye, EyeOff } from 'lucide-react';
import { useTranslation } from 'react-i18next';

interface InputProps extends InputHTMLAttributes<HTMLInputElement> {
  id?: string;
  name?: string;
  label?: string;
  type?: string;
  className?: string;
  describedBy?: string;
}

const Input = forwardRef<HTMLInputElement, InputProps>(({
  id,
  name,
  label,
  type = 'text',
  placeholder = '',
  value,
  onChange,
  onBlur,
  required = false,
  disabled = false,
  className = '',
  autoComplete,
  describedBy,
  ...rest
}, ref) => {
  const { t } = useTranslation();
  const [showPassword, setShowPassword] = useState(false);
  const isPasswordType = type === 'password';
  const inputType = isPasswordType && showPassword ? 'text' : type;

  const toggleVisibility = () => setShowPassword((prev) => !prev);
  const resolvedAutoComplete = autoComplete ?? (isPasswordType ? 'current-password' : 'on');

  return (
    <div className="mb-4 flex flex-col">
      {label && (
        <label htmlFor={id} className="mb-1 text-xs font-bold text-[#0e2238]">
          {label}
        </label>
      )}
      <div className="relative flex items-center">
        <input
          ref={ref}
          id={id}
          name={name}
          type={inputType}
          placeholder={placeholder}
          value={value}
          onChange={onChange}
          onBlur={onBlur}
          required={required}
          disabled={disabled}
          autoComplete={resolvedAutoComplete}
          aria-describedby={describedBy}
          className={`w-full rounded-lg border border-[#edeef0] bg-[#f8fafb] px-3 pr-10 py-2 text-xs font-normal text-[#0e2238] transition-all focus:border-[#007bff] focus:outline-none focus:ring-1 focus:ring-[#007bff] disabled:bg-[#e9ecef] ${className}`}
          {...rest}
        />
        {isPasswordType && (
          <button
            type="button"
            onClick={toggleVisibility}
            className="absolute right-3 p-0 text-[#555] hover:text-[#333]"
            aria-label={showPassword ? t('common.hidePassword', 'Hide Password') : t('common.showPassword', 'Show Password')}
          >
            {showPassword ? <Eye className="w-4 h-4" /> : <EyeOff className="w-4 h-4" />}
          </button>
        )}
      </div>
    </div>
  );
});

Input.displayName = 'Input';

export default Input;
