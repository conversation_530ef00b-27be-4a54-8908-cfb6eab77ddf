import React, { useState, useEffect, forwardRef, useImperativeHandle } from 'react';
import { useTranslation } from 'react-i18next';
import reloadIcon from '../../assets/images/reload-icon.png';

interface CaptchaProps {
  onChange: (value: string) => void;
}

const generateCaptcha = () => {
  const chars = '0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ';
  let captcha = '';
  for (let i = 0; i < 6; i++) {
    captcha += chars.charAt(Math.floor(Math.random() * chars.length));
  }
  return captcha;
};

const Captcha = forwardRef(({ onChange }: CaptchaProps, ref) => {
  const { t } = useTranslation();
  const [captcha, setCaptcha] = useState(generateCaptcha());

  const refreshCaptcha = () => {
    const newCaptcha = generateCaptcha();
    setCaptcha(newCaptcha);
    onChange(newCaptcha);
  };

  useImperativeHandle(ref, () => ({
    refreshCaptcha,
  }));

  useEffect(() => {
    onChange(captcha);
  }, [captcha, onChange]);

  return (
    <div className="flex items-center gap-[5rem]">
      <div
        tabIndex={0}
        aria-live="polite"
        aria-atomic="true"
        role="status"
        style={{ fontFamily: '"Courier New", Courier, monospace' }}
        className="font-mono font-bold text-[24px] bg-[repeating-linear-gradient(0deg,#fdfdfd,#fdfdfd_2px,#eee_2px,#eee_4px)] 
          px-[15px] py-[1px] w-full border border-gray-300 tracking-[0.1875rem] select-none"
      >
        {captcha}
        <span className="sr-only">
          {t('auth.captchaSpoken', { code: captcha.split('').join(' ') })}
        </span>
      </div>

      <button
        type="button"
        aria-label={t('common.reloadCaptcha')}
        onClick={refreshCaptcha}
        className="bg-transparent border-none cursor-pointer p-0"
      >
        <img
          src={reloadIcon}
          alt={t('common.reloadCaptcha')}
          className="w-16 h-auto transition-transform duration-200 ease-in-out hover:rotate-[90deg]"
        />
      </button>
    </div>
  );
});

export default Captcha;
