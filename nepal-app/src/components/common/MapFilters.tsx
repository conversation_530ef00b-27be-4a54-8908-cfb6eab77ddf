import React, { useRef } from 'react';
import { useTranslation } from 'react-i18next';
import FilledMapPin from './FilledMapPin';

type FilterType = 1 | 2 | 3 | 4 | 5 | 6 | 7;

type FilterBarProps = {
  selectedFilter: FilterType;
  setSelectedFilter: React.Dispatch<React.SetStateAction<FilterType>>;
};

const FILTERS: { key: FilterType; title: string; color: string; hexColor: string }[] = [
  { key: 1, title: 'basic', color: 'text-red-500', hexColor: '#ef4444' },
  { key: 2, title: 'primary', color: 'text-orange-500', hexColor: '#f97316' },
  { key: 3, title: 'secondary', color: 'text-lime-500', hexColor: '#84cc16' },
  { key: 4, title: 'tertiary', color: 'text-red-500', hexColor: '#ef4444' },
  { key: 5, title: 'private', color: 'text-red-500', hexColor: '#6b7280' },
  { key: 6, title: 'ayurvedic', color: 'text-gray-500', hexColor: '#6b7280' },
  { key: 7, title: 'laboratory', color: 'text-sky-600', hexColor: '#0284c7' },
];

export default function FilterBar({ selectedFilter, setSelectedFilter }: FilterBarProps) {
  const { t } = useTranslation();
  const buttonRefs = useRef<(HTMLButtonElement | null)[]>([]);

  const handleKeyDown = (e: React.KeyboardEvent, index: number) => {
    let nextIndex = index;

    if (e.key === 'ArrowRight') {
      nextIndex = (index + 1) % FILTERS.length;
    } else if (e.key === 'ArrowLeft') {
      nextIndex = (index - 1 + FILTERS.length) % FILTERS.length;
    } else {
      return;
    }

    e.preventDefault();
    setSelectedFilter(FILTERS[nextIndex].key);
    buttonRefs.current[nextIndex]?.focus();
  };

  return (
    <div
      className="flex justify-center gap-1 items-center my-4 flex-wrap"
      role="radiogroup"
      aria-label={t('filterBar.label')}
    >
      <h2 className="sr-only">{t('filterBar.label')}</h2>

      {FILTERS.map(({ key, title, hexColor }, index) => {
        const isSelected = selectedFilter === key;

        return (
          <button
            key={key}
            ref={(el) => {
              buttonRefs.current[index] = el;
            }}
            role="radio"
            aria-checked={isSelected}
            aria-label={t(`filterBar.filters.${title}`)}
            tabIndex={isSelected ? 0 : -1}
            onClick={() => setSelectedFilter(key)}
            onKeyDown={(e) => handleKeyDown(e, index)}
            className={`flex items-center gap-2 px-8 py-2 rounded-md focus:outline-none focus-visible:ring-2 focus-visible:ring-offset-2 focus-visible:ring-blue-500 ${
              isSelected
                ? 'bg-blue-600 text-white font-semibold shadow-md'
                : 'bg-transparent text-blue-700 hover:text-blue-800'
            }`}
          >
            <FilledMapPin size={30} color={hexColor} dotColor="#fff" dotSize={8} />
            <span>{t(`filterBar.filters.${title}`)}</span>
          </button>
        );
      })}
    </div>
  );
}
