import React from 'react';
import type { ButtonHTMLAttributes, ReactNode } from 'react';
import clsx from 'clsx';

export type ButtonVariant = 'primary' | 'secondary' | 'outline' | 'text' | 'danger' | 'success';
export type ButtonSize = 'sm' | 'md' | 'lg';

export interface ButtonProps extends ButtonHTMLAttributes<HTMLButtonElement> {
  children: ReactNode;
  variant?: ButtonVariant;
  size?: ButtonSize;
  fullWidth?: boolean;
  isLoading?: boolean;
  leftIcon?: ReactNode;
  rightIcon?: ReactNode;
  className?: string;
  ariaLabel?: string;
}

const Button: React.FC<ButtonProps> = ({
  children,
  variant = 'primary',
  size = 'md',
  fullWidth = false,
  isLoading = false,
  leftIcon,
  rightIcon,
  className = '',
  disabled,
  type = 'button',
  ariaLabel,
  ...rest
}) => {
  const base =
    'inline-flex items-center justify-center rounded-full font-medium transition transform focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-60 disabled:cursor-not-allowed';

  const sizeClasses = {
    sm: 'h-8 px-3 text-sm',
    md: 'h-10 px-4 text-base',
    lg: 'h-12 px-5 text-lg',
  };

  const variantClasses = {
    primary: 'bg-blue-500 text-white hover:bg-blue-600 focus:ring-blue-400',
    secondary: 'bg-gray-500 text-white hover:bg-gray-600 focus:ring-gray-400',
    outline: 'border border-blue-600 text-blue-600 bg-transparent hover:bg-blue-50 focus:ring-blue-400',
    text: 'text-blue-600 bg-transparent px-2 hover:bg-blue-50 focus:ring-blue-400',
    danger: 'bg-rose-600 text-white hover:bg-rose-700 focus:ring-rose-500',
    success: 'bg-emerald-500 text-white hover:bg-emerald-600 focus:ring-emerald-400',
  };

  const spinner = (
    <span
      className="absolute inset-0 flex items-center justify-center"
      aria-hidden="true"
    >
      <span className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin" />
    </span>
  );

  return (
    <button
      type={type}
      disabled={disabled || isLoading}
      aria-disabled={disabled || isLoading}
      aria-busy={isLoading || undefined}
      aria-label={ariaLabel}
      className={clsx(
        base,
        sizeClasses[size],
        variantClasses[variant],
        fullWidth && 'w-full',
        isLoading && 'relative text-transparent',
        className
      )}
      {...rest}
    >
      {isLoading && spinner}
      <span className="flex items-center justify-center gap-2">
        {leftIcon && <span className="flex items-center">{leftIcon}</span>}
        <span className="whitespace-nowrap">{children}</span>
        {rightIcon && <span className="flex items-center">{rightIcon}</span>}
      </span>
    </button>
  );
};

export default Button;
