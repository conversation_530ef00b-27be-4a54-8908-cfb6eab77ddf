import React, { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useLocation } from '../../context/LocationContext';

export type Level = 'country' | 'province' | 'district' | 'municipality' | 'ward';

export interface Filters {
  province: string;
  district: string;
  municipality: string;
  ward: string;
  country?: string;
}

interface GeoFilterProps {
  value: Filters;
  level: Level;
  onChange: (filters: Filters, level: Level) => void;
}

const showSelect: Record<Level, Level[]> = {
  country: [],
  province: ['province'],
  district: ['province', 'district'],
  municipality: ['province', 'district', 'municipality'],
  ward: ['province', 'district', 'municipality', 'ward'],
};

const GeoFilter: React.FC<GeoFilterProps> = ({ value, level, onChange }) => {
  const { t } = useTranslation();
  const [selectedLevel, setSelectedLevel] = useState<Level>(level);
  const [filters, setFilters] = useState<Filters>(value);
  
  // Access flatLocations data from the context
  const { flatLocations } = useLocation();

  const [provinces, setProvinces] = useState<any[]>([]);
  const [districts, setDistricts] = useState<any[]>([]);
  const [municipalities, setMunicipalities] = useState<any[]>([]);
  const [wards, setWards] = useState<any[]>([]);

  const [districtOptions, setDistrictOptions] = useState<any[]>([]);
  const [municipalityOptions, setMunicipalityOptions] = useState<any[]>([]);
  const [wardOptions, setWardOptions] = useState<any[]>([]);

  useEffect(() => {
    if (flatLocations) {
      setProvinces(flatLocations.provinces || []);
      setDistricts(flatLocations.districts || []);
      setMunicipalities(flatLocations.municipalities || []);
      setWards(flatLocations.wards || []);
    }
  }, [flatLocations]);

  useEffect(() => setSelectedLevel(level), [level]);
  useEffect(() => setFilters(value), [value]);

  // Helper: get province ID by name
  const provinceId = provinces.find(p => p.name === filters.province)?.id;
  const districtId = districts.find(d => d.name === filters.district)?.id;
  const municipalityId = municipalities.find(m => m.name === filters.municipality)?.id;

  // District options: filter by province if set, else show all
  useEffect(() => {
    if (provinceId) {
      setDistrictOptions(districts.filter(d => d.province === provinceId));
    } else {
      setDistrictOptions(districts);
    }
  }, [provinceId, districts]);

  // Municipality options: filter by district if set, else show all
  useEffect(() => {
    if (districtId) {
      setMunicipalityOptions(municipalities.filter(m => m.district === districtId));
    } else {
      setMunicipalityOptions(municipalities);
    }
  }, [districtId, municipalities]);

  // Ward options: filter by municipality if set, else show all
  useEffect(() => {
    if (filters.municipality && municipalityId) {
      setWardOptions(wards.filter(w => w.municipality === municipalityId));
    } else {
      setWardOptions([]);
    }
  }, [filters.municipality, municipalityId, wards]);

  // When a filter changes, only clear filters below the changed one
  const handleFilterChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    const { name, value } = e.target;
    let updated = { ...filters, [name]: value };

    switch (name) {
      case 'province':
        updated.district = '';
        updated.municipality = '';
        updated.ward = '';
        break;
      case 'district':
        updated.municipality = '';
        updated.ward = '';
        break;
      case 'municipality':
        updated.ward = '';
        break;
      case 'ward':
        break;
    }

    setFilters(updated);
    onChange(updated, selectedLevel);
  };

  const handleLevelChange = (newLevel: Level) => {
    setSelectedLevel(newLevel);
    onChange(filters, newLevel);
  };

  return (
    <fieldset className="flex flex-row gap-20 text-sm mb-4" role="radiogroup" aria-labelledby="geo-level-legend">
      <legend id="geo-level-legend" className="sr-only">
        {t('common.level')}
      </legend>
      {(Object.keys(showSelect) as Level[])
        .filter(levelOption => levelOption !== 'country')
        .map(levelOption => {
          const labelId = `level-${levelOption}`;
          const selectId = `select-${levelOption}`;

          return (
            <div key={levelOption} className="flex flex-col items-start gap-2">
              <label htmlFor={labelId} className="flex items-center w-50 gap-2">
                <input
                  id={labelId}
                  type="radio"
                  name="level"
                  value={levelOption}
                  checked={selectedLevel === levelOption}
                  onChange={() => handleLevelChange(levelOption)}
                />
                <span className="capitalize">{t(`common.${levelOption}`)}</span>
              </label>

              {showSelect[selectedLevel].includes(levelOption) && (
                <select
                  id={selectId}
                  name={levelOption}
                  aria-label={t(`common.select${levelOption.charAt(0).toUpperCase() + levelOption.slice(1)}`)}
                  value={filters[levelOption]}
                  onChange={handleFilterChange}
                  className="appearance-none px-4 py-2 w-50 text-xs rounded-lg border border-[#edeef0] bg-[#f8fafb] font-normal text-[#0e2238] transition-all focus:border-blue-400 focus:outline-none focus:ring-1 focus:ring-blue-400"
                >
                  <option value="">
                    {t(`common.select${levelOption.charAt(0).toUpperCase() + levelOption.slice(1)}`)}
                  </option>

                  {levelOption === 'province' &&
                    provinces.map(p => (
                      <option key={p.id} value={p.name}>
                        {p.name}
                      </option>
                    ))}

                  {levelOption === 'district' &&
                    districtOptions.map(d => (
                      <option key={d.id} value={d.name}>
                        {d.name}
                      </option>
                    ))}

                  {levelOption === 'municipality' &&
                    municipalityOptions.map(m => (
                      <option key={m.id} value={m.name}>
                        {m.name}
                      </option>
                    ))}

                  {levelOption === 'ward' &&
                    wardOptions.map(w => (
                      <option key={w.id} value={w.name}>
                        {w.name}
                      </option>
                    ))}
                </select>
              )}
            </div>
          );
        })}
    </fieldset>
  );
};

export default GeoFilter;
