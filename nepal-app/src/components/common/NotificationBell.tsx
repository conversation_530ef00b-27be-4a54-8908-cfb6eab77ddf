import React from 'react';
import { Bell } from 'lucide-react';
import { dummyNotifications } from '../../constants/notifications';
import { useTranslation } from 'react-i18next';

type NotificationBellProps = {
  onClick?: () => void;
};

const NotificationBell: React.FC<NotificationBellProps> = ({ onClick }) => {
  const { t } = useTranslation();
  const unreadCount = dummyNotifications.filter(n => !n.isRead).length;

  const ariaLabel = unreadCount > 0
    ? t('notifications.unread', { count: unreadCount })
    : t('notifications.none');

  return (
    <div
      className="relative cursor-pointer"
      onClick={onClick}
      aria-label={ariaLabel}
    >
      <Bell className="h-6 w-6 text-gray-700" aria-hidden="true" />
      {unreadCount > 0 && (
        <span className="absolute -top-1 -right-1 bg-red-600 text-white text-[10px] font-semibold px-1.5 py-0.5 rounded-full">
          {unreadCount > 99 ? '99+' : unreadCount}
        </span>
      )}
    </div>
  );
};

export default NotificationBell;
