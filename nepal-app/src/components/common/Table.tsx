import React from "react";
import { useTranslation } from 'react-i18next';

export type Column<T> = {
  header: string;
  accessor: keyof T;
  className?: string;
  render?: (value: any, row: T, index: number) => React.ReactNode;
};

type TableProps<T> = {
  columns: Column<T>[];
  data: T[];
  title?: string;
  loading?: boolean;
  getRowKey?: (row: T, index: number) => string;
};

export function Table<T extends object>({
  columns,
  data,
  title,
  loading = false,
  getRowKey,
}: TableProps<T>) {
  const { t } = useTranslation();

  return (
    <div className="rounded-2xl overflow-hidden border border-gray-200 shadow-sm bg-white">
      {title && (
        <div className="bg-gray-50 px-6 py-4 border-b border-gray-200">
          <h2 className="text-base font-semibold text-gray-800 uppercase">{title}</h2>
        </div>
      )}
      <div className="w-full overflow-x-auto">
        <table
          role="table"
          className="min-w-full table-auto text-sm text-left text-gray-800 whitespace-nowrap"
        >
          {title && <caption className="sr-only">{title}</caption>}
          <thead className="bg-gray-100 text-gray-600 text-sm font-medium">
            <tr>
              {columns.map((col) => (
                <th
                  key={String(col.accessor)}
                  scope="col"
                  className={`px-6 py-3 ${col.className || ""}`}
                >
                  {col.header}
                </th>
              ))}
            </tr>
          </thead>
          <tbody>
            {loading ? (
              <tr>
                <td colSpan={columns.length} className="px-6 py-4 text-center">
                  {t('common.loading')}
                </td>
              </tr>
            ) : data.length === 0 ? (
              <tr>
                <td colSpan={columns.length} className="px-6 py-4 text-center">
                  {t('common.noDataAvailable')}
                </td>
              </tr>
            ) : (
              data.map((row, idx) => (
                <tr
                  key={getRowKey ? getRowKey(row, idx) : idx}
                  className="even:bg-gray-50 hover:bg-gray-100 transition"
                >
                  {columns.map((col) => (
                    <td
                      key={String(col.accessor)}
                      className={`px-6 py-4 ${col.className || ""}`}
                    >
                      {col.render
                        ? col.render(row[col.accessor], row, idx)
                        : String(row[col.accessor] ?? "")}
                    </td>
                  ))}
                </tr>
              ))
            )}
          </tbody>
        </table>
      </div>
    </div>
  );
}
