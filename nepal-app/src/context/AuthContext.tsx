import {
  createContext,
  useState,
  useContext,
  useEffect,
} from 'react';
import type { ReactNode } from 'react';
import {
  login as loginService,
  logout as logoutService,
  getCurrentUser,
  verifyToken,
} from '../api/services/auth.service';
import axiosInstance from '../api/axios';

interface User {
  id: number;
  username: string;
  name: string;
  role: string;
  email: string;
  authority_level?: string;
}

interface AuthContextType {
  user: User | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  login: (username: string, password: string) => Promise<void>;
  logout: () => Promise<void>;
  updateUser: (user: Partial<User>) => void;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const AuthProvider = ({ children }: { children: ReactNode }) => {
  const [user, setUser] = useState<User | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    const initAuth = async () => {
      const token = localStorage.getItem('authToken');
      if (!token) {
        setIsLoading(false);
        return;
      }

      try {
        const isValid = await verifyToken();
        if (isValid) {
          const userData = await getCurrentUser();
          setUser(userData);
        } else {
          localStorage.removeItem('authToken');
        }
      } catch {
        localStorage.removeItem('authToken');
      } finally {
        setIsLoading(false);
      }
    };

    initAuth();
  }, []);

  const loginHandler = async (username: string, password: string) => {
    const response = await loginService(username, password);
    const loginData = response.loginData
    const userData = {
      id: loginData.user.id,
      username: loginData.user.username,
      name: loginData.user.full_name,
      role: loginData.user.role || 'staff',
      email: loginData.user.email,
    };

    localStorage.setItem('authToken', loginData.accessToken);
    setUser(userData);
  };

  const logoutHandler = async () => {
    setIsLoading(true);
    try {
      await logoutService();
    } finally {
      localStorage.removeItem('authToken');
      setUser(null);
      setIsLoading(false);
    }
  };

  const updateUserHandler = (userData: Partial<User>) => {
    if (user) {
      setUser({ ...user, ...userData });
    }
  };

  return (
    <AuthContext.Provider
      value={{
        user,
        isAuthenticated: !!user,
        isLoading,
        login: loginHandler,
        logout: logoutHandler,
        updateUser: updateUserHandler,
      }}
    >
      {children}
    </AuthContext.Provider>
  );
};

export const useAuth = (): AuthContextType => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};
