import React, { createContext, useContext, useEffect, useState } from 'react';
import axiosInstance from '../api/axios';
import { useAuth } from './AuthContext';

// Interfaces for Location Data
export interface Ward {
  id: number;
  name: string;
  code: string;
  ward_number: number;
  is_active?: boolean;
  municipality_name?: string;
  district_name?: string;
  province_name?: string;
  municipality?: number;
  district?: number;
  province?: number;
}

export interface Municipality {
  id: number;
  name: string;
  code: string;
  municipality_type: string | number;
  municipality_type_name?: string;
  is_active?: boolean;
  district_name?: string;
  province_name?: string;
  district?: number;
  province?: number;
  wards?: Ward[];
}

export interface District {
  id: number;
  name: string;
  code: string;
  is_active?: boolean;
  province_name?: string;
  province: number;
  municipalities?: Municipality[];
}

export interface Province {
  id: number;
  name: string;
  code: string;
  is_active?: boolean;
  country?: number;
  country_name?: string;
  districts?: District[];
}

export interface Country {
  id: number;
  name: string;
  code: string;
  provinces: Province[];
}

interface FlatLocationData {
  provinces: Province[];
  districts: District[];
  municipalities: Municipality[];
  wards: Ward[];
}

interface LocationContextType {
  countries: Country[];
  flatLocations: FlatLocationData;
  getProvinces: (countryId: number) => Province[];
  getDistricts: (provinceId: number) => District[];
  getMunicipalities: (provinceId: number, districtId: number) => Municipality[];
  getWards: (provinceId: number, districtId: number, municipalityId: number) => Ward[];
  getProvinceNameById: (provinceId: number) => string | undefined;
  getDistrictNameById: (districtId: number) => string | undefined;
  getMunicipalityNameById: (municipalityId: number) => string | undefined;
  getWardNameById: (wardId: number) => string | undefined;
  getNameById: (level: string, id: number) => string | undefined;
}

// Context
const LocationContext = createContext<LocationContextType | undefined>(undefined);

// Hook
export const useLocation = () => {
  const context = useContext(LocationContext);
  if (!context) {
    throw new Error('useLocation must be used within a LocationProvider');
  }
  return context;
};

// Provider
export const LocationProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [countries, setCountries] = useState<Country[]>([]);
  const [flatLocations, setFlatLocations] = useState<FlatLocationData>({ provinces: [], districts: [], municipalities: [], wards: [] });
  const { isAuthenticated } = useAuth();

  const CACHE_KEY = 'location_hierarchy';
  const CACHE_FLAT_KEY = 'location_flat';
  const CACHE_EXPIRY_KEY = 'location_hierarchy_expiry';
  const CACHE_DURATION = 1000 * 60 * 60 * 24; // 24 hours

  // Fetching and caching hierarchical location data
  useEffect(() => {
    if (!isAuthenticated) return;

    const cachedHierarchy = localStorage.getItem(CACHE_KEY);
    const cachedFlat = localStorage.getItem(CACHE_FLAT_KEY);
    const expiry = localStorage.getItem(CACHE_EXPIRY_KEY);

    if (cachedHierarchy && cachedFlat && expiry && Date.now() < Number(expiry)) {
      try {
        const parsedHierarchy = JSON.parse(cachedHierarchy);
        const parsedFlat = JSON.parse(cachedFlat);
        setCountries(parsedHierarchy);
        setFlatLocations(parsedFlat);
        return;
      } catch (e) {
        console.warn('Corrupt cached location data. Refetching...');
      }
    }

    const fetchLocationData = async () => {
      try {
        const hierarchyRes = await axiosInstance.get('/location/hierarchy');
        const flatRes = await axiosInstance.get('/location/flat');
        
        const hierarchyData = hierarchyRes.data.data.countries;
        const flatData = flatRes.data.data;

        setCountries(hierarchyData);
        setFlatLocations(flatData);

        localStorage.setItem(CACHE_KEY, JSON.stringify(hierarchyData));
        localStorage.setItem(CACHE_FLAT_KEY, JSON.stringify(flatData));
        localStorage.setItem(CACHE_EXPIRY_KEY, (Date.now() + CACHE_DURATION).toString());
      } catch (error) {
        console.error('Failed to load location data:', error);
      }
    };

    fetchLocationData();
  }, [isAuthenticated]);

  // Helper functions for filtering data
  const getProvinces = (countryId: number) =>
    countries.find(c => c.id === countryId)?.provinces || [];

  const getDistricts = (provinceId: number) =>
    countries.flatMap(c => c.provinces)
      .find(p => p.id === provinceId)?.districts || [];

  const getMunicipalities = (provinceId: number, districtId: number) =>
    getDistricts(provinceId).find(d => d.id === districtId)?.municipalities || [];

  const getWards = (provinceId: number, districtId: number, municipalityId: number) =>
    getMunicipalities(provinceId, districtId)
      .find(m => m.id === municipalityId)?.wards || [];

  // const getProvinceIdByName = (countryId: number, provinceName: string): number | undefined =>
  //   countries.find(c => c.id === countryId)
  //     ?.provinces.find(p => p.name.toLowerCase() === provinceName.toLowerCase())?.id;

  // const getDistrictIdByName = (provinceId: number, districtName: string): number | undefined =>
  //   countries.flatMap(c => c.provinces)
  //     .find(p => p.id === provinceId)
  //     ?.districts.find(d => d.name.toLowerCase() === districtName.toLowerCase())?.id;

  // const getMunicipalityIdByName = (
  //   provinceId: number,
  //   districtId: number,
  //   municipalityName: string
  // ): number | undefined =>
  //   getDistricts(provinceId).find(d => d.id === districtId)
  //     ?.municipalities.find(m => m.name.toLowerCase() === municipalityName.toLowerCase())?.id;

  // const getWardIdByName = (
  //   provinceId: number,
  //   districtId: number,
  //   municipalityId: number,
  //   wardName: string
  // ): number | undefined =>
  //   getMunicipalities(provinceId, districtId)
  //     .find(m => m.id === municipalityId)
  //     ?.wards.find(w => w.name.toLowerCase() === wardName.toLowerCase())?.id;

  const getProvinceNameById = (provinceId: number): string | undefined => {
    return flatLocations.provinces.find(p => p.id === provinceId)?.name;
  };
  
  const getDistrictNameById = (districtId: number): string | undefined => {
    return flatLocations.districts.find(d => d.id === districtId)?.name;
  };
  
  const getMunicipalityNameById = (municipalityId: number): string | undefined => {
    return flatLocations.municipalities.find(m => m.id === municipalityId)?.name;
  };
  
  const getWardNameById = (wardId: number): string | undefined => {
    return flatLocations.wards.find(w => w.id === wardId)?.name;
  };

  const getNameById = (level: string | 'ward', id: number): string | undefined => {
    switch (level) {
      case 'province':
        return flatLocations.provinces.find(p => p.id === id)?.name;
      case 'district':
        return flatLocations.districts.find(d => d.id === id)?.name;
      case 'municipality':
        return flatLocations.municipalities.find(m => m.id === id)?.name;
      case 'ward':
        return flatLocations.wards.find(w => w.id === id)?.name;
      default:
        return 'Nepal';
    }
  };  
  
  return (
    <LocationContext.Provider
      value={{
        countries,
        flatLocations,
        getProvinces,
        getDistricts,
        getMunicipalities,
        getWards,
        getProvinceNameById,
        getDistrictNameById,
        getMunicipalityNameById,
        getWardNameById,
        getNameById
      }}
    >
      {children}
    </LocationContext.Provider>
  );
};
