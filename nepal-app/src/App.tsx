import React, { useEffect, useState } from 'react';
import AppRouter from './routes';
import i18n from './i18n/config';
import { useLoading } from './context/LoaderContext';
import Loader from './components/common/Loader';
import { registerLoadingSetter } from './api/loaderHookBridge';

const App: React.FC = () => {
  const [language, setLanguage] = useState(i18n.language || 'en');
  const { isLoading, setLoading } = useLoading();

  useEffect(() => {
    document.documentElement.lang = language;
  }, [language]);

  useEffect(() => {
    const handleLanguageChanged = (lng: string) => {
      setLanguage(lng);
    };

    i18n.on('languageChanged', handleLanguageChanged);

    return () => {
      i18n.off('languageChanged', handleLanguageChanged);
    };
  }, []);

  useEffect(() => {
    registerLoadingSetter(setLoading);
  }, [setLoading]);

  return (
    <>
      <Loader isLoading={isLoading} />
      <AppRouter />
    </>
  );
};

export default App;
