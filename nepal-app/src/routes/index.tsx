import React from 'react';
import { Routes, Route, Navigate } from 'react-router-dom';
import { useAuth } from '../context/AuthContext';
import PrivateRoute from './PrivateRoute';
import PageLayout from '../components/layout/PageLayout';
import LoginPage from '../pages/LoginPage';
import DashboardPage from '../pages/DashboardPage';
import GeoprofilePage from '../pages/GeoprofilePage';
import GeographyPage from '../pages/GeographyPage';
import ReportPage from '../pages/ReportPage';
import SetPasswordPage from '../features/auth/components/SetPasswordPage';
import VaccineStoragePage from '../pages/VaccineStoragePage';
import NotificationPage from '../pages/NotificationPage';
import ChangePassword from '../features/auth/components/LoginForm/ChangePassword';
import ManageUsers from '../pages/ManageUsersPage';
import FacilityDetails from '../pages/FacilityDetailsPage';
import InvalidTokenPage from '../pages/InvalidTokenPage';
import AddUser from '../pages/AddUsersPage';
import EditUser from '../pages/EditUsersPage';
import UserView from '../pages/UserViewPage';
import DisableUser from '../pages/DisableUsersPage';
import Loader from '../components/common/Loader';
import FacilityPage from '../pages/FacilityPage';
import Unauthorized from '../pages/UnauthorizedPage';
import PopulationPage from '../pages/Population';
import RequireRoleAccess from '../components/auth/RequireRoleAccess';


const AppRouter: React.FC = () => {
  const { isAuthenticated, isLoading } = useAuth();

  return (
    <>
    <Routes>
      {/* Public route */}
      <Route path="/login" element={<LoginPage />} />
      <Route path="/set-password" element={<SetPasswordPage />} />
      <Route path="/accept-invitation/:token" element={<SetPasswordPage/>} />
      <Route path="/invalid-token" element={<InvalidTokenPage/>} />
      <Route path="/unauthorized" element={<Unauthorized />} />

      {/* Protected routes */}
      <Route element={<PrivateRoute />}>
        <Route element={<PageLayout />}>
          <Route path="/dashboard" element={<DashboardPage />} />
          <Route path="/geo-profile" element={<GeoprofilePage />} />
          <Route path="/geography" element={<GeographyPage/>} />
          <Route path="/report" element={<ReportPage />} />
          <Route path="/change-password" element={<ChangePassword />} />
          <Route path="/notifications" element={<NotificationPage />} />
          <Route path="/population" element={<PopulationPage />} />
          <Route path="/manage-users" element={
            <RequireRoleAccess requiredAuthority='center'>
              <ManageUsers />
            </RequireRoleAccess>
          } />
          <Route
            path="/user/new"
            element={
              <RequireRoleAccess requiredAuthority="center" requiredPosition="Admin">
                <AddUser />
              </RequireRoleAccess>
            } />
          <Route
            path="/user/edit/:id"
            element={
              <RequireRoleAccess requiredAuthority="center" requiredPosition="Admin">
                <EditUser />
              </RequireRoleAccess>
            }
          />
          <Route
            path="/user/disable/:id"
            element={
              <RequireRoleAccess requiredAuthority="center" requiredPosition="Admin">
                <DisableUser />
              </RequireRoleAccess>
            }
          />
          <Route path="/user/:id" element={<UserView />} />
          <Route path="/facilities/:id" element={<FacilityDetails />} />
          <Route path="/vaccine-storage" element={<VaccineStoragePage/>} />
          <Route path="/facility" element={<FacilityPage/>} />
          
        </Route>
      </Route>

      {/* Root redirect */}
      <Route 
        path="/" 
        element={
          isAuthenticated 
            ? <Navigate to="/dashboard" replace /> 
            : <Navigate to="/login" replace />
        } 
      />
    </Routes>
    <Loader isLoading={isLoading} />
    </>
  );
};

export default AppRouter;