import React from 'react';
import { Navigate, Outlet, useLocation } from 'react-router-dom';
import { useAuth } from '../context/AuthContext';
import Loader from '../components/common/Loader';

interface PrivateRouteProps {
  redirectPath?: string;
  children?: React.ReactNode;
}

/**
 * A route that requires authentication.
 * If the user is not authenticated, they will be redirected to the specified path.
 */
const PrivateRoute: React.FC<PrivateRouteProps> = ({
  redirectPath = '/login',
  children
}) => {
  const { isAuthenticated, isLoading } = useAuth();
  const location = useLocation();

  // Show loading indicator while checking authentication
  if (isLoading) {
    return (
      <Loader isLoading={isLoading} />
    );
  }

  // If not authenticated, redirect to login with the current location in state
  if (!isAuthenticated) {
    return (
      <Navigate 
        to={redirectPath} 
        state={{ from: location.pathname }} 
        replace 
      />
    );
  }

  // If authenticated, render children or outlet
  return (
    <>
      {children ? <>{children}</> : <Outlet />}
      <Loader isLoading={isLoading} />
    </>
  );
};

export default PrivateRoute;