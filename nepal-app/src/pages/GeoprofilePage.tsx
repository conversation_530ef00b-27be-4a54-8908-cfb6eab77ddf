import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import GeoFilter from '../components/common/GeoFilter';
import GeographySection from '../features/geo-profile/GeographySection';
import FacilitySection from '../features/geo-profile/FacilitySection';
import SpecializedUnitsSection from '../features/geo-profile/SpecializedUnitsSection';
import GeoprofileMap from '../features/geo-profile/GeoprofileMap';
import StaffSection from '../features/geo-profile/HrSection';
import HmisDashboard from '../features/geo-profile/HmisData';
import ProgramData from '../features/geo-profile/ProgramData';
import { NCDStats } from '../features/geo-profile/NcdData';
import Loader from '../components/common/Loader';
import type { Filters, Level } from '../components/common/GeoFilter';

const GeoprofilePage = () => {
  const { t } = useTranslation();

  const [geoFilters, setGeoFilters] = useState<Filters>({
    province: null,
    district: null,
    municipality: null,
    ward: null,
  });
  const [geoLevel, setGeoLevel] = useState<Level>('province');

  const [appliedFilters, setAppliedFilters] = useState<{ filters: Filters; level: Level } | null>(null);

  const handleFilterClick = () => {
    if (geoFilters.province) {
      setAppliedFilters({ filters: geoFilters, level: geoLevel });
    }
  };
  return (
    <>
      <title>{t('geoprofile.pageTitle')}</title>
      {/* bg-[#f8f9fa]  */}
      <div className="space-y-7 my-6 mx-5 bg-white rounded-xl shadow-lg py-6 px-6 mb-6">
        <h3 className="text-xl font-bold">
          {t('geoprofile.title', 'Geoprofile')}
        </h3>
        <div>
          <h5 className="text-xs font-semibold mb-5 tracking-wide text-gray-700">
            {t('geoprofile.profileView')}
          </h5>
          <div className="flex items-center mb-4">
            <div className="w-1/6">
              <h4 className="text-left text-xs font-bold text-blue-700">
                {t('geoprofile.userProfile')}
              </h4>
            </div>
            <div className="w-5/6">
              <h6 className="text-left text-xs text-pink-500 font-bold">
                {/* {t('geoprofile.countryLevelLogin', { country: 'Nepal' })} */}
                {/* {t('appadmin', 'Admin')} */}
              </h6>
            </div>
          </div>

          {appliedFilters && (
            <div className="flex items-center mb-5">
              <div className="w-1/6">
                <h4 className="text-left text-xs font-bold text-blue-700">
                  {t('geoprofile.searchCriteria')}
                </h4>
              </div>
              <div className="w-5/6">
                <h6 className="text-left text-xs text-pink-500 font-bold flex flex-wrap items-center gap-1">
                  {appliedFilters.filters.province && (
                    <>
                      {t('geoprofile.province')}: {appliedFilters.filters.province}
                    </>
                  )}
                  {appliedFilters.filters.district && (
                    <>
                      <span className="text-gray-400">→</span> {t('geoprofile.district')}: {appliedFilters.filters.district}
                    </>
                  )}
                  {appliedFilters.filters.municipality && (
                    <>
                      <span className="text-gray-400">→</span> {t('geoprofile.palika')}: {appliedFilters.filters.municipality}
                    </>
                  )}
                  {appliedFilters.filters.ward && (
                    <>
                      <span className="text-gray-400">→</span> {t('geoprofile.ward')}: {appliedFilters.filters.ward}
                    </>
                  )}
                </h6>
              </div>
            </div>
          )}
        </div>
        <div className="border-b border-gray-300 mb-6" />
        <GeoFilter
          value={geoFilters}
          level={geoLevel}
          onChange={(filters, level) => {
            setGeoFilters(filters);
            setGeoLevel(level);
          }}
        />
        <button
          className="w-[100%] mx-auto block bg-[#ebcf4f] hover:bg-[#c8b043] transition-colors duration-300 text-white font-semibold py-2.5 rounded-3xl text-sm shadow-md"
          type="button"
          onClick={handleFilterClick}
        >
          {t('geoprofile.populateData')}
        </button>
      </div>

      {appliedFilters && (
        <div className="bg-white mx-5 rounded-xl shadow-lg">
          <GeographySection />
          <FacilitySection />
          <SpecializedUnitsSection />
          <GeoprofileMap />
          <StaffSection />
          <ProgramData />
          {/* <NCDStats /> */}
        </div>
      )}
    </>
  );
};

export default GeoprofilePage;
