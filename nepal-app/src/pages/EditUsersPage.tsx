import React, { useEffect, useState } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import UserForm from '../features/manage-users/UserForm';
import type { UserFormData } from '../features/manage-users/UserForm';
import { getUserById } from '../api/services/user.service';
import type { User } from '../api/services/user.service';
import { updateUser } from '../api/services/user.service';

const EditUser: React.FC = () => {
  const { t } = useTranslation();
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();

  const [defaultValues, setDefaultValues] = useState<UserFormData | undefined>(undefined);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (!id) {
      setError(t('user.invalidUserId', 'Invalid user ID'));
      setLoading(false);
      return;
    }

    const fetchUser = async () => {
      try {
        const user: User = await getUserById(Number(id));
        // Map the API user object to UserFormData shape
        const mapped: UserFormData = {
          fname: user.fname,
          mname: '', // you can adjust if you have this info
          lname: user.lname,
          email: user.email,
          username: user.username || '',
          phone: user.phone || '',
          role: user.role,
          position_id: user.positionId || 1,
          authority_location_id: user.authorityLocationId,
        };
        setDefaultValues(mapped);
      } catch (err) {
        console.error('Failed to load user:', err);
        setError(t('user.failedToLoad', 'Failed to load user data'));
      } finally {
        setLoading(false);
      }
    };

    fetchUser();
  }, [id, t]);

  const handleSubmit = async (data: UserFormData) => {
    if (!id) return;
  
    try {
      const payload = {
        first_name: data.fname,
        last_name: data.lname,
        email: data.email,
        username: data.username,
        authority_level: data.role,
        authority_location_id: data.authority_location_id,
        position: data.position_id,
      };
  
      await updateUser(Number(id), payload);
  
      navigate('/manage-users');
    } catch (err) {
      console.error('Failed to update user:', err);
      setError(t('user.failedToUpdate', 'Failed to update user'));
    }
  };  

  if (loading) return <p>{t('common.loading', 'Loading...')}</p>;
  if (error) return <p className="text-red-600">{error}</p>;

  return (
    <div className="p-6 bg-white rounded-xl shadow-lg mx-6 min-h-screen">
      <UserForm
        mode="edit"
        defaultValues={defaultValues}
        onSubmit={handleSubmit}
        onCancel={() => navigate('/manage-users')}
      />
    </div>
  );
};

export default EditUser;