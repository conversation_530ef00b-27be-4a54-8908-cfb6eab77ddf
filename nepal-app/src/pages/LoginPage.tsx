import React, { useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '../context/AuthContext';
import PublicNavbar from '../components/layout/PublicNavbar';
import signinCover from '../assets/images/immunization.jpg';
import LoginForm from '../features/auth/components/LoginForm/LoginForm';
import { useTranslation } from 'react-i18next';

const LoginPage: React.FC = () => {
  const navigate = useNavigate();
  const { login, isLoading, isAuthenticated } = useAuth();
  const { t } = useTranslation();

  useEffect(() => {
    document.title = t('auth.loginPageTitle');
  }, [t]);

  useEffect(() => {
    if (!isLoading && isAuthenticated) {
      navigate('/dashboard');
    }
  }, [isAuthenticated, isLoading, navigate]);

  const handleLogin = async (
    username: string,
    password: string,
  ) => {
    await login(username, password);
  };

  return (
    <div className="h-screen flex flex-col overflow-hidden">
      <header>
        <PublicNavbar />
      </header>

      <main className="h-screen flex" aria-labelledby="login-page-heading">
        <section className="flex-1 flex items-center justify-center bg-gray-50 px-8">
          <h1 id="login-page-heading" className="sr-only">
            {t('auth.loginPageTitle')}
          </h1>

          <LoginForm onSubmit={handleLogin} isLoading={isLoading} />
        </section>

        <aside className="flex-1 relative" aria-hidden="true">
          <div
            className="absolute inset-0 bg-cover bg-center bg-no-repeat"
            style={{ backgroundImage: `url(${signinCover})` }}
          />
          <div className="absolute inset-0 bg-[#0e2238]/60" />
          <div className="absolute bottom-8 left-8 text-white">
            <p className="text-sm opacity-80" aria-hidden="true">
              {t('auth.secureAccess')}
            </p>
          </div>
        </aside>
      </main>
    </div>
  );
};

export default LoginPage;
