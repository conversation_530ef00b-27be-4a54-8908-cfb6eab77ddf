import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import GeoFilter from '../components/common/GeoFilter';
import type { Filters as GeoFilters, Level } from '../components/common/GeoFilter';
import FacilityFilter from '../components/common/FacilityFilter';
import { getFacilities } from '../api/services/facility.service';
import { PaginatedTable } from '../components/common/PaginatedTable';
import { EyeIcon } from 'lucide-react';
import { useNavigate } from 'react-router-dom';

interface FacilityItem {
  id: string;
  province: string;
  district: string;
  palika: string;
  ward: string;
  facilityName: string;
  facilityType: string;
  facilityCode: string;
}
const FacilityPage: React.FC = () => {
  const { t } = useTranslation();
  const navigate = useNavigate();

  // Geo filters
  const [geoFilters, setGeoFilters] = useState<GeoFilters>({
    province: null,
    district: null,
    municipality: null,
    ward: null,
  });
  const [geoLevel, setGeoLevel] = useState<Level>('country');
  const [applyGeo, setApplyGeo] = useState(false);

  // Facility filters
  const [facilityType, setFacilityType] = useState<number | undefined>(undefined);
  const [facilityLevel, setFacilityLevel] = useState<number | undefined>(undefined);
  const [authorityLevel, setAuthorityLevel] = useState<number | undefined>(undefined);

  // Facility results
  const [facilities, setFacilities] = useState<FacilityItem[]>([]);
  const [loading, setLoading] = useState(false);

  useEffect(() => {
      document.title = t('faciltiy.pageTitle', 'Nepal Immunization | Facility');
    }, [t]);

  // Fetch facilities whenever filters change
  useEffect(() => {
    const fetchData = async () => {
      setLoading(true);
      try {
        const params: any = {};
        if (facilityType) params.facility_type_id = facilityType;
        if (facilityLevel) params.facility_level_id = facilityLevel;
        if (authorityLevel) params.authority_level_id = authorityLevel;
        if (applyGeo) {
          if (geoFilters.province) params.province_id = geoFilters.province;
          if (geoFilters.district) params.district_id = geoFilters.district;
          if (geoFilters.municipality) params.municipality_id = geoFilters.municipality;
          if (geoFilters.ward) params.ward_id = geoFilters.ward;
        }
        const result = await getFacilities(params);
        const mapped = (result.results || []).map(f => ({
          id: f.id,
          province: f.province?.name || '',
          district: f.district?.name || '',
          palika: f.municipality?.name || '',
          ward: f.ward?.name || '',
          facilityName: f.name,
          facilityType: f.facility_type?.name || '',
          facilityCode: f.hf_code || '',
        }));
        setFacilities(mapped);
      } catch (err) {
        console.error(err);
        setFacilities([]);
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [facilityType, facilityLevel, authorityLevel, geoFilters, applyGeo]);

  const columnConfig = [
    { header: 'SL No', accessor: 'slNo' },
    { header: 'Province', accessor: 'province' },
    { header: 'District', accessor: 'district' },
    { header: 'Palika', accessor: 'palika' },
    { header: 'Ward', accessor: 'ward' },
    { header: 'Facility Name', accessor: 'facilityName' },
    { header: 'Facility Type', accessor: 'facilityType' },
    { header: 'Facility Code', accessor: 'facilityCode' },
    {
      header: 'Actions',
      accessor: 'actions' as any,
      render: (_: any, row: FacilityItem) => (
        <button
          onClick={() => navigate(`/facilities/${row.id}`)}
          title="View Details"
        >
          <EyeIcon className="w-4 h-4 text-blue-600" />
        </button>
      ),
    },
  ];
  

  const tableData = facilities.map((f, idx) => ({ slNo: idx + 1, ...f }));

  return (
    <div className="space-y-7 my-6 mx-5 bg-white rounded-xl shadow-lg py-6 px-6 mb-6">
      <h1 className="text-xl font-semibold">{t('Facility Data')}</h1>

      <div className=" space-y-4">
        <GeoFilter
          value={geoFilters}
          level={geoLevel}
          onChange={(gf, lvl) => {
            setGeoFilters(gf);
            setGeoLevel(lvl);
          }}
        />
        <button
          onClick={() => setApplyGeo(true)}
          className="px-4 py-2  my-2 w-full bg-blue-500 text-white rounded-2xl"
        >
          {t('Filter Data')}
        </button>
      </div>

        <FacilityFilter
          facilityType={facilityType}
          setFacilityType={setFacilityType}
          facilityLevel={facilityLevel}
          setFacilityLevel={setFacilityLevel}
          authority={authorityLevel}
          setAuthority={setAuthorityLevel}
        />
      <div className="border-b border-gray-300 my-6"></div>

      <PaginatedTable columns={columnConfig} data={tableData} />
    </div>
  );
};

export default FacilityPage;
