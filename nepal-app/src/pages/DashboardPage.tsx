import React, { useState, useMemo } from 'react';
import GeographyInfo from '../features/dashboard/components/GeographyInfo';
import FacilityData from '../features/dashboard/components/FacilityData';
import ProgramData from '../features/dashboard/components/ProgramData';
import HrData from '../features/dashboard/components/HrData';
import GeoFilter from '../components/common/GeoFilter';
import type { Filters, Level } from '../components/common/GeoFilter';
import { useTranslation } from 'react-i18next';
import { SlidersHorizontal } from 'lucide-react';
import Loader from '../components/common/Loader';
import DashboardMap from '../features/dashboard/components/DashboardMap';

const Dashboard: React.FC = () => {
  const { t } = useTranslation();

  // Memoize defaultFilters so its reference doesn't change on every render
  const defaultFilters = useMemo<Filters>(() => ({
    province: null,
    district: null,
    municipality: null,
    ward: null,
  }), []);

  const [filters, setFilters] = useState<Filters>(defaultFilters);
  const [level, setLevel] = useState<Level>('country');
  const [showFilter, setShowFilter] = useState<boolean>(false);
  const [shouldPopulate, setShouldPopulate] = useState<boolean>(false);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [selectedMonth, setSelectedMonth] = useState<string>("");

  const handleGeoFilterChange = (updatedFilters: Filters, updatedLevel: Level) => {
    setFilters(updatedFilters);
    setLevel(updatedLevel);
    setShouldPopulate(false); // Reset when filters change
  };

  const handleMonthChange = (month: string) => {
    setSelectedMonth(month);
    console.log(selectedMonth);
  };

  const handlePopulateClick = () => {
    setIsLoading(true);
    setShouldPopulate(true);
    setTimeout(() => {
      setIsLoading(false);
    }, 1000);
  };

  // Memoize activeFilters to avoid passing new object every render
  const activeFilters = useMemo(() => (shouldPopulate ? filters : defaultFilters), [shouldPopulate, filters, defaultFilters]);
  const activeLevel = shouldPopulate ? level : 'country';

  console.log('filters');
  console.log(activeFilters);
  console.log('level');
  console.log(activeLevel);
  
  return (
    <>
      <title>{t('dashboard.pageTitle')}</title>
      <div className="min-h-screen py-6 px-8">
        {/* Top bar: Month and Filter toggle */}
        <div className="flex justify-end items-center gap-10 mb-4 mr-2">
          <button
            onClick={() => setShowFilter(prev => !prev)}
            className="flex flex-row justify-center items-center bg-white border border-gray-300 rounded-md px-3 py-1 cursor-pointer
                 text-gray-700 hover:text-gray-900 hover:border-gray-500"
          >
            <SlidersHorizontal className="w-4 h-4 mr-2" />
            <div className="text-base font-medium select-none">{t('common.filter', 'Filter')}</div>
          </button>
        </div>

        {/* Filter panel */}
        {showFilter && (
          <div className="space-y-7 my-6 bg-white rounded-xl shadow-lg py-6 px-6 mb-6">
            <GeoFilter value={filters} level={level} onChange={handleGeoFilterChange} monthFilter={true} onMonthChange={handleMonthChange} />
            <button
              className="w-full mx-auto block bg-[#ebcf4f] hover:bg-[#c8b043] transition-colors duration-300 text-white font-semibold py-2.5 rounded-3xl text-sm shadow-md"
              type="button"
              onClick={handlePopulateClick}
            >
              {t('geoprofile.populateData')}
            </button>
          </div>
        )}

        {/* Data components using applied filters */}
        <GeographyInfo filters={activeFilters} level={activeLevel} />
        <DashboardMap filters={filters} level={level} />
        <ProgramData selectedMonth={selectedMonth} filters={activeFilters} level={activeLevel} />
        <HrData />
        {/* <FacilityData filters={activeFilters} level={activeLevel} /> */}
        <Loader isLoading={isLoading} />
      </div>
    </>
  );
};

export default Dashboard;
