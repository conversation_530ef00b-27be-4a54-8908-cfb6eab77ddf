import React, { useEffect, useState, useRef } from 'react';
import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router-dom';
import { useProfile } from '../context/ProfileContext';
import Input from '../components/common/Input';
import UserTable from '../features/manage-users/UserTable';
import { getUsers } from '../api/services/user.service';
import type { User } from '../api/services/user.service';
import DisableUserModal from '../features/manage-users/DisableUserModal';

const ManageUsers: React.FC = () => {
  const { t } = useTranslation();
  const navigate = useNavigate();
  const searchInputRef = useRef<HTMLInputElement>(null);
  const { profile } = useProfile();

  const [users, setUsers] = useState<User[]>([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [page, setPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);
  const [total, setTotal] = useState(0);
  const [loading, setLoading] = useState(false);

  // State for DisableUserModal
  const [disableModalOpen, setDisableModalOpen] = useState(false);
  const [selectedUserId, setSelectedUserId] = useState<number | null>(null);
  const [selectedUserName, setSelectedUserName] = useState('');

  const fetchUsers = async () => {
    try {
      setLoading(true);
      const res = await getUsers({
        page_number: page,
        page_size: pageSize,
        search: searchTerm,
      });
      setUsers(res.users);
      setTotal(res.total);
    } catch (err) {
      console.error('Failed to fetch users:', err);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    document.title = t('users.pageTitle', 'Nepal Immunization | Users');
  }, [t]);

  useEffect(() => {
    const debounce = setTimeout(() => {
      fetchUsers();
    }, 300);
    return () => clearTimeout(debounce);
  }, [page, pageSize, searchTerm]);

  // Auto-focus search input when component mounts
  useEffect(() => {
    if (searchInputRef.current) {
      searchInputRef.current.focus();
    }
  }, []);

  return (
    <div className="my-8 p-6 bg-white rounded-xl shadow-lg mx-6 min-h-screen">
      <div className="flex justify-between items-center mb-6">
        <h2 className="text-2xl font-semibold">
          {t('manageusers.title', 'Manage Users')}
        </h2>
        {(profile?.authority_level === 'center' && profile?.position === 1) && (
          <button
            onClick={() => navigate('/user/new')}
            className="bg-blue-600 text-white px-4 py-2 rounded"
          >
            + {t('manageusers.addUser', 'Add User')}
          </button>
        )}
      </div>

      <div className="relative w-full max-w-md mb-4">
        <Input
          ref={searchInputRef}
          type="text"
          placeholder={t('manageusers.searchPlaceholder', 'Search users...')}
          className="pl-4"
          value={searchTerm}
          onChange={(e) => {
            setSearchTerm(e.target.value);
            setPage(1);
          }}
        />
      </div>

      <UserTable
        users={users}
        page={page}
        pageSize={pageSize}
        total={total}
        loading={loading}
        onPageChange={setPage}
        onPageSizeChange={setPageSize}
        onEdit={(user) => navigate(`/user/edit/${user.id}`)}
        onDelete={() => {}}
        onDisable={(user) => {
          setSelectedUserId(user.id);
          setSelectedUserName(`${user.fname} ${user.lname}`);
          setDisableModalOpen(true);
        }}
        onView={(user) => navigate(`/user/${user.id}`)}
      />

      <DisableUserModal
        isOpen={disableModalOpen}
        userId={selectedUserId}
        userName={selectedUserName}
        onClose={() => setDisableModalOpen(false)}
        onSuccess={() => {
          fetchUsers(); // Refresh user list after disabling
          setDisableModalOpen(false);
        }}
      />
    </div>
  );
};

export default ManageUsers;