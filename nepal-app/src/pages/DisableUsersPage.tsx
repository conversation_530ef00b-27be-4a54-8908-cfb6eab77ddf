import React, { useEffect, useState } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import { getUserById, disableUser } from '../api/services/user.service';
import type { User } from '../api/services/user.service';

const DisableUser: React.FC = () => {
  const { t } = useTranslation();
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();

  const [user, setUser] = useState<User | null>(null);
  const [loading, setLoading] = useState(true);
  const [reason, setReason] = useState('');
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState(false);

  useEffect(() => {
    const fetchUser = async () => {
      if (!id) {
        setError(t('user.invalidUserId', 'Invalid user ID'));
        setLoading(false);
        return;
      }
      try {
        const fetchedUser = await getUserById(Number(id));
        setUser(fetchedUser);
      } catch (err) {
        console.error(err);
        setError(t('user.failedToLoad', 'Failed to load user data'));
      } finally {
        setLoading(false);
      }
    };
    fetchUser();
  }, [id, t]);

  const handleDisable = async () => {
    if (!id || !reason.trim()) {
      setError(t('user.provideReason', 'Please provide a reason'));
      return;
    }

    try {
      await disableUser(Number(id), {  });
      setSuccess(true);
      setTimeout(() => navigate('/manage-users'), 1500);
    } catch (err) {
      console.error(err);
      setError(t('user.failedToDisable', 'Failed to disable user'));
    }
  };

  if (loading) return <p>{t('common.loading', 'Loading...')}</p>;
  if (error) return <p className="text-red-600">{error}</p>;
  if (!user) return null;

  return (
    <div className="max-w-xl mx-auto mt-10 p-6 bg-white rounded shadow">
      <h1 className="text-xl font-semibold mb-4">
        {t('user.disableUser', 'Disable User')}: {user.fname} {user.lname}
      </h1>

      <label className="block mb-2 font-medium">
        {t('user.reason', 'Reason for disabling')}
      </label>
      <textarea
        value={reason}
        onChange={(e) => setReason(e.target.value)}
        className="w-full p-2 border border-gray-300 rounded mb-4"
        rows={4}
        placeholder={t('user.enterReason', 'Enter reason here...')}
      />

      <div className="flex gap-4">
        <button
          onClick={handleDisable}
          className="bg-red-600 text-white px-4 py-2 rounded hover:bg-red-700"
        >
          {t('user.disable', 'Disable')}
        </button>
        <button
          onClick={() => navigate('/manage-users')}
          className="bg-gray-300 px-4 py-2 rounded hover:bg-gray-400"
        >
          {t('common.cancel', 'Cancel')}
        </button>
      </div>

      {success && (
        <p className="text-green-600 mt-4">
          {t('user.disabledSuccess', 'User has been disabled successfully.')}
        </p>
      )}
    </div>
  );
};

export default DisableUser;
