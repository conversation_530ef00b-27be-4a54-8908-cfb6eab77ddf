import React, { useEffect, useState } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import { getUserById } from '../api/services/user.service';
import type { User } from '../api/services/user.service';
import { getLocationName } from '../utils/locationHelper';

const UserView: React.FC = () => {
  const { t } = useTranslation();
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();

  const [user, setUser] = useState<User | null>(null);
  const [locationName, setLocationName] = useState<string>('-');
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchUserAndLocation = async () => {
      if (!id) {
        setError(t('userView.errorLoading', 'Failed to load user data.'));
        setLoading(false);
        return;
      }
      try {
        setLoading(true);
        const userData = await getUserById(Number(id));
        setUser(userData);

        let locName = '-';
        try {
          if (userData.authorityLocationId && userData.role !== 'center') {
            locName = await getLocationName(userData.role, userData.authorityLocationId);
          } else if (userData.role === 'center') {
            locName = 'Center';
          }
        } catch {
          locName = '-';
        }
        setLocationName(locName);
      } catch (err) {
        console.error('Error loading user data:', err);
        setError(t('userView.errorLoading', 'Failed to load user data.'));
      } finally {
        setLoading(false);
      }
    };

    fetchUserAndLocation();
  }, [id, t]);

  if (loading) {
    return <div className="text-center p-8">{t('common.loading', 'Loading...')}</div>;
  }

  if (error) {
    return <div className="text-center text-red-600 p-8">{error}</div>;
  }

  if (!user) {
    return <div className="text-center p-8">{t('userView.notFound', 'User not found.')}</div>;
  }

  return (
    <div className="bg-gray-100 min-h-screen px-4 py-8">
      <div className="max-w-2xl mx-auto bg-white rounded-xl shadow-md p-6">
        <div className="flex justify-between items-center mb-6">
          <button
            onClick={() => navigate(-1)}
            className="text-blue-600 hover:underline text-sm"
          >
            &larr; {t('common.back', 'Back')}
          </button>

          <button
            onClick={() => navigate(`/user/edit/${user.id}`)}
            className="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700 text-sm"
          >
            {t('common.edit', 'Edit')}
          </button>
        </div>

        <h2 className="text-2xl font-bold text-gray-800 mb-4">
          {t('userView.title', 'User Details')}
        </h2>

        <div className="space-y-3 text-gray-700 text-base">
          <div>
            <strong>{t('user.name')}</strong>: {[user.fname, user.mname, user.lname].filter(Boolean).join(' ')}
          </div>
          <div>
            <strong>{t('user.email')}</strong>: {user.email}
          </div>
          <div>
            <strong>{t('user.phone')}</strong>: {user.phone ?? '-'}
          </div>
          <div>
            <strong>{t('user.role')}</strong>: {t(`user.roleOptions.${user.role}`)}
          </div>
          <div>
            <strong>{t('user.status')}</strong>: {user.status}
          </div>
          <div>
            <strong>{t('user.createdOn')}</strong>: {new Date(user.createdOn).toLocaleDateString()}
          </div>
          <div>
            <strong>{t('user.location', 'Authority Location')}</strong>: {locationName}
          </div>
        </div>
      </div>
    </div>
  );
};

export default UserView;
