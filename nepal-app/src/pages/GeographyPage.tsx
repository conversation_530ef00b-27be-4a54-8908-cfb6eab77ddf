import React, { useState, useEffect, useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import GeoFilter from '../components/common/GeoFilter';
import type { Filters, Level } from '../components/common/GeoFilter';
// import GeographyTable from '../features/report/components/GeographyReportTable';

const GeographyPage: React.FC = () => {
  const { t } = useTranslation();

  // Initialize filters with IDs (empty string or number)
  const defaultFilters = useMemo<Filters>(() => ({
      province: null,
      district: null,
      municipality: null,
      ward: null,
    }), []);
  
    const [filters, setFilters] = useState<Filters>(defaultFilters);

  const [level, setLevel] = useState<Level>('province');
  const [showReport, setShowReport] = useState(false);

  useEffect(() => {
    document.title = t('geography.pageTitle', 'Nepal Immunization | Geography');
  }, [t]);

  const handleFilterClick = () => {
    setShowReport(true);
  };

  return (
    <div className="mx-5 my-10">
      <div className="space-y-7 bg-white rounded-xl shadow-lg py-6 px-6 mb-6">
        <h3 className="text-xl font-bold">
          {t('geography.title', 'Geography')}
        </h3>

        <div className="bg-yellow-50 border border-yellow-300 rounded-lg p-5 mb-6">
          <div className="flex flex-wrap gap-4">
            <GeoFilter
              value={filters}
              level={level}
              onChange={(updatedFilters, updatedLevel) => {
                setFilters(updatedFilters);
                setLevel(updatedLevel);
                setShowReport(false);
              }}
            />
          </div>
          <button
            className="w-full bg-yellow-300 text-white font-medium py-2 rounded-md text-sm"
            onClick={handleFilterClick}
          >
            {t('report.filterData')}
          </button>
        </div>
{/* 
        {showReport && (
          <GeographyTable filters={filters} geoLevel={level} />
        )} */}
      </div>
    </div>
  );
};

export default GeographyPage;
