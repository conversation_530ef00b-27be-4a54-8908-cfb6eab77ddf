import React, { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useParams } from 'react-router-dom';
import FilledMapPin from '../components/common/FilledMapPin';
import ServicesSection from '../features/auth/components/facility-details/ServicesSection';
import HrDetails from '../features/auth/components/facility-details/HrDetails';
// import ProgramData from '../features/auth/components/facility-details/ProgramDetails';
import ProgramData from '../features/auth/components/facility-details/ProgramDetails';
import AuthorityDetails from '../features/auth/components/facility-details/AuthorityDetails';
import ContactDetails from '../features/auth/components/facility-details/ContactDetails';
import BedDetails from '../features/auth/components/facility-details/Details';
import VaccinationRecord from '../features/auth/components/facility-details/VaccinationRecord';
import Loader from '../components/common/Loader';
import { Shield, Activity, UserCheck, Baby, Heart, Calendar, Building2, HandHelping, UserLock, PhoneCall, MailIcon, UserCircle, Bed } from 'lucide-react';
import { getFacilityById } from '../api/services/facility.service';
import type { Facility as FacilityAPI } from '../api/services/facility.service';

const FacilityDetails: React.FC = () => {
  const { id } = useParams<{ id: string; }>();
  const { t } = useTranslation();
  const [facility, setFacility] = useState<FacilityAPI | null>(null);

  useEffect(() => {
    if (id) {
      getFacilityById(String(id)).then(setFacility).catch(console.error);
    }
  }, [id]);

  if (!facility) return <Loader isLoading />;

  // Map authority details dynamically
  const detailsData = [
    {
      labelKey: 'authorityLevel',
      icon: <Building2 className="w-5 h-5" strokeWidth={1.5} />,
      valueKey: facility.authority_level?.name || facility.authority || 'N/A',
    },
    {
      labelKey: 'authority',
      icon: <UserLock className="w-5 h-5" strokeWidth={1.5} />,
      valueKey: facility.authority || 'N/A',
    },
    {
      labelKey: 'ownership',
      icon: <HandHelping className="w-5 h-5" strokeWidth={1.5} />,
      valueKey: facility.ownership.name || 'N/A',
    },
  ];

  const contactDetailsData = [
    {
      labelKey: 'contactName',
      icon: <UserCircle className="w-5 h-5" strokeWidth={1.5} />,
      valueKey: facility.contact_person || facility.authority || '--',
    },
    {
      labelKey: 'contactEmail',
      icon: <MailIcon className="w-5 h-5" strokeWidth={1.5} />,
      valueKey: facility.email || '--',
    },
    {
      labelKey: 'contactMobile',
      icon: <PhoneCall className="w-5 h-5" strokeWidth={1.5} />,
      valueKey: facility.contact_person_mobile || '--',
    },
  ];

  const bedDetails = [
    {
      label1Key: 'bedCount',
      icon: <Bed className="w-5 h-5" strokeWidth={1.5} />,
      value1Key: facility.bed_count || '--',
      label2Key: 'functionalBedCount',
      value2Key: facility.functional_bed_count || '--',
    },
  ];

  const stats = [
    {
      icon: <Shield />,
      value: 2380,
      labelKey: "vaccinationRecord.totalVaccinesAdministered",
      subLabelKey: "vaccinationRecord.doses",
      colorBg: "bg-blue-50",
      colorBorder: "border-blue-100",
      colorText: "text-blue-600",
    },
    {
      icon: <Activity />,
      value: 28,
      labelKey: "vaccinationRecord.vaccinesWasted",
      subLabelKey: "vaccinationRecord.estimatedDoses",
      colorBg: "bg-orange-50",
      colorBorder: "border-orange-100",
      colorText: "text-orange-600",
    },
    {
      icon: <UserCheck />,
      value: "1.17%",
      labelKey: "vaccinationRecord.wastageRate",
      subLabelKey: "vaccinationRecord.withinNationalThreshold",
      subLabelClassName: "text-green-600 font-medium",
      colorBg: "bg-green-50",
      colorBorder: "border-green-100",
      colorText: "text-green-600",
    },
    {
      icon: <Baby />,
      value: 250,
      labelKey: "vaccinationRecord.fullyImmunizedChildren",
      subLabelKey: "vaccinationRecord.age0to23Months",
      colorBg: "bg-purple-50",
      colorBorder: "border-purple-100",
      colorText: "text-purple-600",
    },
  ];
  
  const vaccineDetails = [
    {
      titleKey: "vaccinationRecord.mostAdministeredVaccine",
      vaccineName: "OPV",
      doses: 395,
      colorBg: "bg-teal-50",
      colorBorder: "border-teal-100",
      colorText: "text-teal-700",
    },
    {
      titleKey: "vaccinationRecord.leastAdministeredVaccine",
      vaccineName: "TCV",
      doses: 60,
      colorBg: "bg-amber-50",
      colorBorder: "border-amber-100",
      colorText: "text-amber-700",
    },
  ];
  
  const statusIndicators = [
    {
      icon: <Heart />,
      titleKey: "vaccinationRecord.coldChainStatus",
      descriptionKey: "vaccinationRecord.noColdChainBreaches",
      colorBg: "bg-green-50",
      colorBorder: "border-green-200",
      colorText: "text-green-600",
      statusIcon: "✅",
    },
    {
      icon: <Calendar />,
      titleKey: "vaccinationRecord.reportingCompliance",
      descriptionKey: "vaccinationRecord.allMonthsSubmitted",
      colorBg: "bg-blue-50",
      colorBorder: "border-blue-200",
      colorText: "text-blue-600",
      statusIcon: "✅",
    },
    ];

    return (
      <div className="bg-white min-h-screen rounded-2xl shadow-2xl m-7">
        <div className="bg-white relative p-8 shadow-sm mb-6 rounded-t-2xl">
          <div className="flex items-start space-x-3">
            <FilledMapPin size={30} />
            <div>
              <h1 className="text-2xl font-math font-semibold text-gray-900">
                {facility.name}
              </h1>
              <p className="text-sm text-gray-600 ml-5 mt-1">
                {t('common.province', 'Province')}: {facility.province?.name} → {t('common.district', 'District')}: {facility.district?.name} → {t('common.municipality', 'Municipality')}: {facility.municipality?.name} → {t('common.ward', 'Ward')}: {facility.ward?.name}
              </p>
              <p className="text-xl ml-5 text-gray-800 mt-1">
                {t('facilityDetails.hfCodePrefix', 'HF')}-{facility.hf_code}
                {facility.hmis_code && (
                  <> / {t('facilityDetails.hmisCodePrefix', 'HMIS')}-{facility.hmis_code}</>
                )}
              </p>
            </div>
          </div>
        </div>
        <div className="px-6">
          <AuthorityDetails details={detailsData} />
          <BedDetails bedDetails={bedDetails} />
          <ContactDetails contactDetails={contactDetailsData} />
          {/* <ServicesSection facility={facility} /> */}
          <HrDetails />
          <ProgramData facilityId={id} />
          {/* <VaccinationRecord
            period="April 2023 – March 2024"
            stats={stats}
            vaccineDetails={vaccineDetails}
            statusIndicators={statusIndicators}
          /> */}
        </div>
    </div>
  );
};

export default FacilityDetails;
