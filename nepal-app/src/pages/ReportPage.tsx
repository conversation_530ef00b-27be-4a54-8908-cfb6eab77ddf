import React, { useState, useEffect, useRef, useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import CategoryFilter from '../components/common/CategoryFilter';
import { useQueryParams } from '../features/report/hooks/useQuery';
import GeoFilter from '../components/common/GeoFilter';
import type { Filters, Level } from '../components/common/GeoFilter';
import DynamicReportTable from '../features/report/components/DynamicReportTable';

const ReportPage: React.FC = () => {
  const { t } = useTranslation();
  const { category, filter, setCategory, setFilter } = useQueryParams();

  const [uploadedFileName, setUploadedFileName] = useState<string | null>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const handleButtonClick = () => {
    fileInputRef.current?.click();
  };

  const [showFilters, setShowFilters] = useState(false);
  const defaultFilters = useMemo<Filters>(() => ({
    province: null,
    district: null,
    municipality: null,
    ward: null,
  }), []);

  const [geofilters, setGeoFilters] = useState<Filters>(defaultFilters);
  const [geoLevel, setGeoLevel] = useState<Level>('country');
  const [shouldPopulate, setShouldPopulate] = useState<boolean>(false);
  const [searchClicked, setSearchClicked] = useState(false);

  // Called when Filter Data button is clicked
  const handleGeoFilterSubmit = () => {
    console.log('Submitted Geo Filters:');
    console.log(geofilters);
    console.log('Geo Level:');
    console.log(geoLevel)

    setShouldPopulate(true);
    setSearchClicked(true);
  };

  // Run only once on mount if query params are present
  useEffect(() => {
    if (category && filter && filter.trim() !== '') {
      setSearchClicked(true);
    }
  }, []); // run only once on component mount

  const handleSearch = () => {
    if (filter && filter.trim() !== '') {
      setSearchClicked(true);
    }
  };

  const handleCategoryChange = (val: string) => {
    setCategory(val);
    setSearchClicked(false);
  };

  const handleFilterChange = (val: string) => {
    setFilter(val);
    setSearchClicked(false);
  };

  const handleFileUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      const validTypes = [
        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        'text/csv',
        'application/vnd.ms-excel',
      ];

      const isValidType =
        validTypes.includes(file.type) ||
        file.name.endsWith('.xlsx') ||
        file.name.endsWith('.csv');

      if (isValidType) {
        console.log('File uploaded:', file);
        setUploadedFileName(file.name);
      } else {
        alert('Please upload a valid Excel (.xlsx) or CSV (.csv) file.');
      }
    }
  };

  return (
    <div className="my-10">
      <title>{t('report.pageTitle')}</title>
      <div className="mx-5">
        <div className="space-y-7 bg-white rounded-xl shadow-lg py-6 px-6 mb-6">
          <div>
            <h2 className="text-lg font-semibold">
              {t('report.title')}
            </h2>

            <div className="flex flex-wrap items-center justify-between gap-4 mb-4">
              <a
                href="#"
                className="text-blue-600 text-xs cursor-pointer"
                onClick={(e) => {
                  e.preventDefault();
                  setShowFilters((prev) => !prev);
                }}
              >
                {t('report.toggleFilter')}
              </a>

              {category === 'Facility' && (
                <div>
                  <input
                    ref={fileInputRef}
                    type="file"
                    id="fileUpload"
                    accept=".xlsx, .csv"
                    onChange={handleFileUpload}
                    className="hidden"
                  />

                  <button
                    type="button"
                    onClick={handleButtonClick}
                    className="text-sm font-semibold text-blue-700 bg-blue-50 hover:bg-blue-100 py-2 px-4 rounded-full focus:outline-none focus-visible:ring focus-visible:ring-blue-300"
                  >
                    {uploadedFileName ?? t('report.uploadFileButtonText', 'Upload File')}
                  </button>
                </div>
              )}
            </div>

            {showFilters && (
              <div className="bg-yellow-50 border border-yellow-300 rounded-lg p-5 mb-6">
                <div className="flex flex-wrap gap-4">
                  <GeoFilter
                    value={geofilters}
                    level={geoLevel}
                    onChange={(filters, level) => {
                      setGeoFilters(filters);
                      setGeoLevel(level);
                      setShouldPopulate(false);
                    }}
                  />
                </div>
                <button
                  onClick={handleGeoFilterSubmit}
                  className="w-full bg-yellow-300 text-white font-medium py-2 rounded-md text-sm"
                >
                  {t('report.filterData')}
                </button>
              </div>
            )}

            <div className="flex items-center mb-4">
              <div className="w-1/6">
                <h4 className="text-left text-xs font-bold text-blue-700">
                  {t('report.userProfile')}
                </h4>
              </div>
              <div className="w-5/6">
                <h6 className="text-left text-xs text-pink-500 font-bold">
                  {/* Optional subtitle */}
                </h6>
              </div>
            </div>

            <CategoryFilter
              category={category}
              filter={filter}
              onCategoryChange={handleCategoryChange}
              onFilterChange={handleFilterChange}
            />

            <button
              onClick={handleSearch}
              className="bg-blue-500 hover:bg-blue-600 text-white py-2 w-full rounded-2xl font-medium text-sm my-4"
            >
              {t('report.search')}
            </button>

            {searchClicked && filter.trim() !== '' && (
              <DynamicReportTable
                category={category}
                filter={filter}
                geoFilters={geofilters}
                geoLevel={geoLevel}
                shouldPopulate={shouldPopulate}
              />
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default ReportPage;
