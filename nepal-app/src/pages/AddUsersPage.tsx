import React from 'react';
import { useNavigate } from 'react-router-dom';
import UserForm from '../features/manage-users/UserForm';
import type { UserFormData } from '../features/manage-users/UserForm';
import { createUser } from '../api/services/user.service';

const AddUser: React.FC = () => {
  const navigate = useNavigate();

  const handleSubmit = async (data: UserFormData) => {
    try {
      const payload = {
        username: data.username,
        email: data.email,
        first_name: data.fname,
        last_name: data.lname,
        status: data.status || 'active',
        authority_level: data.role,
        phone: data.phone,
        position_id: data.position_id,
        authority_location_id: data.authority_location_id,
      };
      await createUser(payload);
      navigate('/manage-users');
    } catch (err) {
      console.error('Failed to create user:', err);
    }
  };
  

  return (
    <div className="my-8 p-6 bg-white rounded-xl shadow-lg mx-6 min-h-screen">
      <UserForm
        mode="create"
        onSubmit={handleSubmit}
        onCancel={() => navigate('/manage-users')}
      />
    </div>
  );
};

export default AddUser;
