import React from 'react';
import { Link } from 'react-router-dom';
import ErrorModal from '../components/common/ErrorModal';
import { useTranslation } from 'react-i18next';

const InvalidTokenPage: React.FC = () => {
  const { t } = useTranslation();

  return (
    <div className="flex items-center justify-center min-h-screen bg-gray-100 px-4">
      <ErrorModal message={t('token.invalidMessage')} />
    </div>
  );
};

export default InvalidTokenPage;

