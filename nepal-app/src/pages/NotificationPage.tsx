import { useTranslation } from "react-i18next";
import { dummyNotifications } from "../constants/notifications";

export default function NotificationPage() {
  const { t } = useTranslation();

  return (
    <>
      <title>{t('notifications.pageTitle')}</title>
      <div className="max-w-7xl mx-6 my-6 p-6 border bg-white border-gray-200 rounded-md shadow-sm transition ">
        <h1 className="text-2xl font-semibold mb-6">{t("Notifications")}</h1>

        {dummyNotifications.length === 0 ? (
          <p className="text-gray-500">{t("notifications.noNotifications")}</p>
        ) : (
          <ul className="space-y-4">
            {dummyNotifications.map((notif) => {
              const message = t(`notifications.${notif.type}`, {
                ...notif.data,
                defaultValue: t("notifications.UNKNOWN"),
              });

              const isRead = notif.isRead;

              return (
              <>
                <li
                  key={notif.id}
                  className={`p-4 border border-gray-200 rounded-md shadow-sm transition 
                    ${isRead 
                      ? "bg-white text-gray-500" 
                      : "bg-gray-50 text-gray-900 font-semibold"}`}
                >
                  
                  <p className="text-sm">
                  {!notif.isRead && (
                  <span className="inline-block w-2 h-2 bg-blue-600 rounded-full mr-2 align-middle" />
                  )}
                      {message}
                      </p>
                  <p className="text-xs text-gray-400 mt-1">{notif.time}</p>
                </li>
              </>
              );
            })}
          </ul>
        )}
      </div>
    </>
  );
}
