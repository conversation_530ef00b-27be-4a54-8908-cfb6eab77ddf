import React, { useState } from 'react';
import GeoInfo from '../features/dashboard/components/GeoInfo';
import DashboardMap from '../features/dashboard/components/DashboardMap';
import FacilityData from '../features/dashboard/components/FacilityData';
import ProgramData from '../features/dashboard/components/ProgramData';
import GeoFilter from '../components/common/GeoFilter';
import type { Filters, Level } from '../components/common/GeoFilter';
import HrData from '../features/dashboard/components/HrData';
import { useTranslation } from 'react-i18next';
import { Filter, SlidersHorizontal } from 'lucide-react';
import FilterBar from '../components/common/FacilityFilters';
import type { FilterType } from '../components/common/FacilityFilters';

import Loader from '../components/common/Loader';

const Dashboard: React.FC = () => {
  const { t } = useTranslation();

  const defaultFilters: Filters = {
    province: null,
    district: null,
    municipality: null,
    ward: null,
  };

  // Current selection state
  const [filters, setFilters] = useState<Filters>(defaultFilters);
  const [level, setLevel] = useState<Level>('country');
  const [selectedFilter, setSelectedFilter] = useState<FilterType>(1);

  // Applied (committed) state for data display
  const [appliedFilters, setAppliedFilters] = useState<Filters>(defaultFilters);
  const [appliedLevel, setAppliedLevel] = useState<Level>('country');

  const [showFilter, setShowFilter] = useState<boolean>(false);
  const [isLoading, setIsLoading] = useState<boolean>(false);

  const handleGeoFilterChange = (updatedFilters: Filters, updatedLevel: Level) => {
    setFilters(updatedFilters);
    setLevel(updatedLevel);
  };

  const handlePopulateClick = () => {
    setIsLoading(true);

    // Simulate loading delay
    setTimeout(() => {
      setAppliedFilters(filters);
      setAppliedLevel(level);
      setIsLoading(false);
    }, 1000);
  };
  console.log('hello');
  console.log('Applied Filter:', appliedFilters);

  return (
    <>
      <title>{t('dashboard.pageTitle')}</title>
      
      <div className="min-h-screen py-6 px-8">
        {/* Filter Toggle Button */}
        <div className="flex justify-end mb-4 mr-2">
          <button
            onClick={() => setShowFilter(prev => !prev)}
            className="flex flex-row justify-center items-center bg-white border border-gray-300 rounded-md px-3 py-1 cursor-pointer
                 text-gray-700 hover:text-gray-900 hover:border-gray-500"
          >
            <SlidersHorizontal className="w-4 h-4 mr-2" />
            <div className="text-base font-medium select-none">{t('common.filter', 'Filter')}</div>
          </button>
        </div>

        {/* Filter Panel */}
        {showFilter && (
          <div className="space-y-7 my-6 bg-white rounded-xl shadow-lg py-6 px-6 mb-6">
            <GeoFilter value={filters} level={level} onChange={handleGeoFilterChange} />
            <button
              className="w-full mx-auto block bg-[#ebcf4f] hover:bg-[#c8b043] transition-colors duration-300 text-white font-semibold py-2.5 rounded-3xl text-sm shadow-md"
              type="button"
              onClick={handlePopulateClick}
            >
              {t('geoprofile.populateData')}
            </button>
          </div>
        )}
        <FilterBar selectedFilter={selectedFilter} setSelectedFilter={setSelectedFilter}/>

        {/* Data Components (show applied state only) */}
        <GeoInfo filters={appliedFilters} level={appliedLevel} />
        {/* <DashboardMap filters={appliedFilters} level={appliedLevel} /> */}
        {/* <ProgramData /> */}
        {/* <HrData /> */}
        {/* <FacilityData filters={appliedFilters} level={appliedLevel} /> */}

        <Loader isLoading={isLoading} />
      </div>
    </>
  );
};

export default Dashboard;
