import { Link } from "react-router-dom";

const Unauthorized = () => {
  return (
    <div className="min-h-screen bg-gray-100 py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-3xl mx-auto">
        <div className="bg-white shadow-md rounded-lg p-6 sm:p-8">
          <h1 className="text-4xl font-extrabold text-red-600 mb-4">
            401 Unauthorized
          </h1>
          <p className="text-lg text-gray-700 mb-6">
            Sorry, you don’t have the necessary permissions to access this page.
            Please check with your administrator if you believe this is an error.
          </p>
          <div>
            <Link
              to="/dashboard"
              className="inline-block bg-blue-600 hover:bg-blue-700 text-white font-semibold py-2 px-4 rounded transition duration-150"
            >
              Return to Dashboard
            </Link>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Unauthorized;
