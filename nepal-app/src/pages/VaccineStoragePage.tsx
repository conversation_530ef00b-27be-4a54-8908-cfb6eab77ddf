import React, { useState, useEffect } from "react";
import { useTranslation } from "react-i18next";
import type { Filters, Level } from "../components/common/GeoFilter";
import GeoFilter from "../components/common/GeoFilter";
import VaccinationRecordMap from "../features/immunization/components/VaccineCenterMap";
import FileUpload from "../components/common/FileUpload";
import { uploadVaccineStorageCenters } from "../api/services/immunization.service";
import StatusModal from "../components/common/StatusModal"; // Adjust path as needed

const VaccineStoragePage: React.FC = () => {
  const { t } = useTranslation();

  const defaultFilters: Filters = {
    province: null,
    district: null,
    municipality: null,
    ward: null,
  };

  const [filters, setFilters] = useState<Filters>(defaultFilters);
  const [level, setLevel] = useState<Level>("country");
  const [isLoading, setIsLoading] = useState<boolean>(false);

  const [appliedFilters, setAppliedFilters] = useState<Filters>(defaultFilters);
  const [appliedLevel, setAppliedLevel] = useState<Level>("country");

  const [modalOpen, setModalOpen] = useState(false);
  const [modalMessage, setModalMessage] = useState('');
  const [modalType, setModalType] = useState<'error' | 'success'>('success');

  const handleGeoFilterChange = (updatedFilters: Filters, updatedLevel: Level) => {
    setFilters(updatedFilters);
    setLevel(updatedLevel);
  };

  const handlePopulateClick = () => {
    setIsLoading(true);
    setAppliedFilters(filters);
    setAppliedLevel(level);
    setIsLoading(false);
  };

  const handleFileUpload = async (file: File) => {
    setIsLoading(true);
    try {
      const response = await uploadVaccineStorageCenters(file);
      setModalMessage(response.detail || 'Upload successful.');
      setModalType('success');
    } catch (error: any) {
      setModalMessage(error?.response?.data?.detail || 'Upload failed. Please try again.');
      setModalType('error');
    } finally {
      setModalOpen(true);
      setIsLoading(false);
    }
  };

  const handleModalClose = () => {
    setModalOpen(false);
  };

  useEffect(() => {
      document.title = t('vaccineStorage.pageTitle', 'Nepal Immunization | Vaccine Storage');
    }, [t]);
  

  return (
    <div className="m-8">
      <div className="space-y-7 my-6 bg-white rounded-xl shadow-lg py-6 px-6 mb-6">
        <h3 className="text-xl font-bold">
          {t('vaccineStorage.title', 'Vaccine Storage')}
        </h3>
        <GeoFilter value={filters} level={level} onChange={handleGeoFilterChange} />
        <button
          className="w-full mx-auto block bg-[#ebcf4f] hover:bg-[#c8b043] transition-colors duration-300 text-white font-semibold py-2.5 rounded-3xl text-sm shadow-md"
          type="button"
          onClick={handlePopulateClick}
          disabled={isLoading}
        >
          {t("geoprofile.populateData")}
        </button>
      </div>

      {/* <div className="flex justify-end items-center gap-10 mb-4 mr-2">
        <FileUpload onFileUpload={handleFileUpload} />
      </div> */}

      <VaccinationRecordMap filters={appliedFilters} level={appliedLevel} />

      {/* Status Modal */}
      <StatusModal
        isOpen={modalOpen}
        onClose={handleModalClose}
        onOk={handleModalClose}
        message={modalMessage}
        type={modalType}
      />
    </div>
  );
};

export default VaccineStoragePage;
