
import React, { useEffect, useState } from 'react';
import { getProvincePopulation, getAllProvincesPopulation } from '../api/services/population.service';
import { PaginatedTable } from '../components/common/PaginatedTable';
import type { Column } from '../components/common/Table';

interface ProvincePopulation {
  id: number;
  province: string;
  population: number;
  wards: number;
  expectedBirths: number;
  expectedPregnancies: number;
  dataYear: number;
}

const PopulationPage: React.FC = () => {
  const [provincePopulation, setProvincePopulation] = useState<ProvincePopulation[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [useHierarchyAPI, setUseHierarchyAPI] = useState(false);

  // Set the auth token for testing (you can remove this once you're logged in)
  React.useEffect(() => {
    const token = localStorage.getItem('authToken');
    if (!token) {
      // Set the provided token for testing
      const testToken = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.XLvW9i6a6ZBdDqSYjx18iOVoX1AtYI89Ilh4F6YrVNE';
      localStorage.setItem('authToken', testToken);
      console.log('✅ Test auth token set for population API testing');
    } else {
      console.log('✅ Auth token already exists in localStorage');
    }
  }, []);

  // Method 1: Fetch provinces first, then individual population data
  const fetchPopulationDataIndividually = async () => {
    console.log('🔄 Starting individual province data fetch...');

    // Hardcoded provinces since location API might not be available
    const provinces = [
      { id: 1, name: 'Koshi' },
      { id: 2, name: 'Madhesh' },
      { id: 3, name: 'Bagmati' },
      { id: 4, name: 'Gandaki' },
      { id: 5, name: 'Lumbini' },
      { id: 6, name: 'Karnali' },
      { id: 7, name: 'Sudurpaschim' },
    ];

    console.log('📍 Using hardcoded provinces:', provinces.map(p => `${p.id}: ${p.name}`).join(', '));

    // Fetch population data for each province using the population service
    const populationData = await Promise.all(
      provinces.map(async (province) => {
        try {
          const response = await getProvincePopulation(province.id);

          if (response.status !== 'success') {
            throw new Error(`API error for province ${province.name}: ${response.message}`);
          }

          const data = response.data;
          return {
            id: province.id,
            province: province.name,
            population: data.total_population || 0,
            wards: data.total_wards || 0,
            expectedBirths: data.total_expected_births || 0,
            expectedPregnancies: data.total_expected_pregnancies || 0,
            dataYear: data.data_year || 2024,
          };
        } catch (err: any) {
          console.error(`Failed to fetch population for province ${province.name}:`, err);
          // Return a default entry with zero values if API call fails
          return {
            id: province.id,
            province: province.name,
            population: 0,
            wards: 0,
            expectedBirths: 0,
            expectedPregnancies: 0,
            dataYear: 2024,
          };
        }
      })
    );

    console.log('✅ Individual province data fetch completed:', populationData.length, 'provinces');
    return populationData;
  };

  // Method 2: Use hierarchy API to get all provinces at once
  const fetchPopulationDataHierarchy = async () => {
    const response = await getAllProvincesPopulation();

    if (response.status !== 'success') {
      throw new Error(`API error: ${response.message}`);
    }

    const populationData = response.data.map((item) => ({
      id: parseInt(item.location_id),
      province: item.location_name,
      population: item.total_population || 0,
      wards: item.total_wards || 0,
      expectedBirths: item.total_expected_births || 0,
      expectedPregnancies: item.total_expected_pregnancies || 0,
      dataYear: item.data_year || 2024,
    }));

    return populationData;
  };

  useEffect(() => {
    const fetchPopulationData = async () => {
      try {
        setLoading(true);
        setError(null);

        console.log(`Fetching population data using ${useHierarchyAPI ? 'Hierarchy' : 'Individual'} API method`);

        let populationData: ProvincePopulation[];

        if (useHierarchyAPI) {
          console.log('Using hierarchy API...');
          populationData = await fetchPopulationDataHierarchy();
        } else {
          console.log('Using individual province API...');
          populationData = await fetchPopulationDataIndividually();
        }

        console.log('Population data fetched successfully:', populationData);

        // Sort by province ID for consistent ordering
        populationData.sort((a, b) => a.id - b.id);
        setProvincePopulation(populationData);
      } catch (err: any) {
        console.error('Failed to fetch population data:', err);
        setError(err.message || 'Failed to load population data. Please try again later.');
      } finally {
        setLoading(false);
      }
    };

    fetchPopulationData();
  }, [useHierarchyAPI]);

  const columns: Column<ProvincePopulation>[] = [
    {
      header: 'Province',
      accessor: 'province',
    },
    {
      header: 'Total Population',
      accessor: 'population',
      render: (value: number) => value > 0 ? value.toLocaleString() : 'No data',
    },
    {
      header: 'Total Wards',
      accessor: 'wards',
      render: (value: number) => value > 0 ? value.toLocaleString() : 'No data',
    },
    {
      header: 'Expected Births',
      accessor: 'expectedBirths',
      render: (value: number) => value > 0 ? value.toLocaleString() : 'No data',
    },
    {
      header: 'Expected Pregnancies',
      accessor: 'expectedPregnancies',
      render: (value: number) => value > 0 ? value.toLocaleString() : 'No data',
    },
    {
      header: 'Data Year',
      accessor: 'dataYear',
    },
  ];

  return (
    <div className="p-6">
      <div className="mb-6">
        <div className="flex justify-between items-start mb-4">
          <div>
            <h1 className="text-3xl font-bold text-gray-900 mb-2">Province Population Data</h1>
            <p className="text-gray-600">
              Population summary for all provinces in Nepal with ward counts and birth projections.
            </p>
          </div>
          <div className="flex items-center space-x-4">
            <div className="flex items-center">
              <label className="text-sm font-medium text-gray-700 mr-2">API Method:</label>
              <button
                onClick={() => setUseHierarchyAPI(!useHierarchyAPI)}
                className={`px-3 py-1 rounded-md text-sm font-medium transition-colors ${
                  useHierarchyAPI
                    ? 'bg-blue-100 text-blue-800 border border-blue-300'
                    : 'bg-gray-100 text-gray-800 border border-gray-300'
                }`}
              >
                {useHierarchyAPI ? 'Hierarchy API' : 'Individual API'}
              </button>
            </div>
            <button
              onClick={() => window.location.reload()}
              className="px-3 py-1 bg-gray-500 text-white rounded-md text-sm font-medium hover:bg-gray-600 transition-colors"
            >
              Refresh
            </button>
          </div>
        </div>
      </div>

      {loading ? (
        <div className="flex items-center justify-center py-12">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
          <span className="ml-3 text-gray-600">Loading population data...</span>
        </div>
      ) : error ? (
        <div className="bg-red-50 border border-red-200 rounded-md p-4">
          <div className="flex">
            <div className="flex-shrink-0">
              <svg className="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
              </svg>
            </div>
            <div className="ml-3">
              <h3 className="text-sm font-medium text-red-800">Error loading data</h3>
              <p className="text-sm text-red-700 mt-1">{error}</p>
            </div>
          </div>
        </div>
      ) : provincePopulation.length === 0 ? (
        <div className="text-center py-12">
          <svg className="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
          </svg>
          <h3 className="mt-2 text-sm font-medium text-gray-900">No population data available</h3>
          <p className="mt-1 text-sm text-gray-500">No population data could be found for any provinces.</p>
        </div>
      ) : (
        <div className="bg-white shadow rounded-lg">
          <div className="px-4 py-5 sm:p-6">
            <div className="mb-4">
              <h3 className="text-lg font-medium text-gray-900">
                Population Summary ({provincePopulation.length} Provinces)
              </h3>
              <div className="flex flex-wrap gap-4 text-sm text-gray-500 mt-1">
                <span>
                  Total Population: {provincePopulation.reduce((sum, p) => sum + p.population, 0).toLocaleString()}
                </span>
                <span>
                  Total Wards: {provincePopulation.reduce((sum, p) => sum + p.wards, 0).toLocaleString()}
                </span>
                <span>
                  Method: {useHierarchyAPI ? 'Hierarchy API' : 'Individual Province API'}
                </span>
              </div>
            </div>
            <PaginatedTable columns={columns} data={provincePopulation} />
          </div>
        </div>
      )}
    </div>
  );
};

export default PopulationPage;