
// import React, { useEffect, useState } from 'react';
// import { getProvinces } from '../api/services/location.service';
// import { getImmunizationDataset } from '../api/services/immunization.service';
// import { PaginatedTable } from '../components/common/PaginatedTable';
// import type { Column } from '../components/common/Table';

// interface ProvincePopulation {
//   province: string;
//   population: number;
// }

// const PopulationPage: React.FC = () => {
//   const [provincePopulation, setProvincePopulation] = useState<ProvincePopulation[]>([]);
//   const [loading, setLoading] = useState(true);

//   useEffect(() => {
//     const fetchPopulationData = async () => {
//       try {
//         const provinces = await getProvinces();
//         const populationData = await Promise.all(
//           provinces.map(async (province) => {
//             const immunizationData = await getImmunizationDataset({ province_id: province.id });
//             const totalPopulation = immunizationData.data.reduce((acc, program) => {
//               const ageGroup = program.categories.find(cat => cat.label.includes('Age Group'));
//               return acc + (ageGroup?.value || 0);
//             }, 0);
//             return {
//               province: province.name,
//               population: totalPopulation,
//             };
//           })
//         );
//         setProvincePopulation(populationData);
//       } catch (error) {
//         console.error('Failed to fetch population data:', error);
//       } finally {
//         setLoading(false);
//       }
//     };

//     fetchPopulationData();
//   }, []);

//   const columns: Column<ProvincePopulation>[] = [
//     {
//       header: 'Province',
//       accessor: 'province',
//     },
//     {
//       header: 'Population',
//       accessor: 'population',
//     },
//   ];

//   return (
//     <div className="p-6">
//       <h1 className="text-2xl font-bold mb-4">Province Population</h1>
//       {loading ? (
//         <p>Loading...</p>
//       ) : (
//         <PaginatedTable columns={columns} data={provincePopulation} />
//       )}
//     </div>
//   );
// };

// export default PopulationPage;


// import React, { useEffect, useState } from 'react';
// import { getProvinces } from '../api/services/location.service';
// import { PaginatedTable } from '../components/common/PaginatedTable';
// import type { Column } from '../components/common/Table';

// interface ProvincePopulation {
//   province: string;
//   population: number;
// }

// const PopulationPage: React.FC = () => {
//   const [provincePopulation, setProvincePopulation] = useState<ProvincePopulation[]>([]);
//   const [loading, setLoading] = useState(true);

//   useEffect(() => {
//     const fetchPopulationData = async () => {
//       try {
//         const provinces = await getProvinces();
//         const populationData = await Promise.all(
//           provinces.map(async (province) => {
//             const response = await fetch(`http://localhost/api/v1/population/data/summary/?province_id=${province.id}`, {
//               headers: {
//                 'Content-Type': 'application/json',
//               },
//             });

//             if (!response.ok) {
//               throw new Error(`Failed to fetch population for province ${province.id}`);
//             }

//             const data = await response.json();
//             return {
//               province: province.name,
//               population: data.data.total_population || 0,
//             };
//           })
//         );
//         setProvincePopulation(populationData);
//       } catch (error) {
//         console.error('Failed to fetch population data:', error);
//       } finally {
//         setLoading(false);
//       }
//     };

//     fetchPopulationData();
//   }, []);

//   const columns: Column<ProvincePopulation>[] = [
//     {
//       header: 'Province',
//       accessor: 'province',
//     },
//     {
//       header: 'Population',
//       accessor: 'population',
//     },
//   ];

//   return (
//     <div className="p-6">
//       <h1 className="text-2xl font-bold mb-4">Province Population</h1>
//       {loading ? (
//         <p>Loading...</p>
//       ) : (
//         <PaginatedTable columns={columns} data={provincePopulation} />
//       )}
//     </div>
//   );
// };

// export default PopulationPage;



// import React, { useEffect, useState } from 'react';
// import { getProvinces } from '../api/services/location.service'; // Assuming this fetches province data
// import { PaginatedTable } from '../components/common/PaginatedTable';
// import type { Column } from '../components/common/Table';

// interface ProvincePopulation {
//   province: string;
//   population: number;
// }

// const PopulationPage: React.FC = () => {
//   const [provincePopulation, setProvincePopulation] = useState<ProvincePopulation[]>([]);
//   const [loading, setLoading] = useState(true);
//   const [error, setError] = useState<string | null>(null);

//   useEffect(() => {
//     const fetchPopulationData = async () => {
//       try {
//         // Fetch provinces from location service
//         const provinces = await getProvinces();

//         // Fetch population data for each province
//         const populationData = await Promise.all(
//           provinces.map(async (province) => {
//             const response = await fetch(
//               `http://localhost:8000/api/v1/population/data/summary/?province_id=${province.id}`,
//               {
//                 headers: {
//                   'Content-Type': 'application/json',
//                   Authorization: `Bearer ${localStorage.getItem('jwt_token') || ''}`, // Retrieve JWT token
//                 },
//               }
//             );

//             if (!response.ok) {
//               throw new Error(`Failed to fetch population for province ${province.name}: ${response.status}`);
//             }

//             const data = await response.json();
//             if (data.status !== 'success') {
//               throw new Error(`API error for province ${province.name}: ${data.message}`);
//             }

//             return {
//               province: province.name,
//               population: data.data.total_population || 0,
//             };
//           })
//         );

//         setProvincePopulation(populationData);
//       } catch (err) {
//         console.error('Failed to fetch population data:', err);
//         setError('Failed to load population data. Please try again later.');
//       } finally {
//         setLoading(false);
//       }
//     };

//     fetchPopulationData();
//   }, []);

//   const columns: Column<ProvincePopulation>[] = [
//     {
//       header: 'Province',
//       accessor: 'province',
//     },
//     {
//       header: 'Total Population',
//       accessor: 'population',
//       render: (value: number) => value.toLocaleString(), // Format number with commas
//     },
//   ];

//   return (
//     <div className="p-6">
//       <h1 className="text-2xl font-bold mb-4">Province Population</h1>
//       {loading ? (
//         <p>Loading...</p>
//       ) : error ? (
//         <p className="text-red-500">{error}</p>
//       ) : provincePopulation.length === 0 ? (
//         <p>No population data available.</p>
//       ) : (
//         <PaginatedTable columns={columns} data={provincePopulation} />
//       )}
//     </div>
//   );
// };

// export default PopulationPage;



import React, { useEffect, useState } from 'react';
import { getProvinces } from '../api/services/location.service';
import { PaginatedTable } from '../components/common/PaginatedTable';
import type { Column } from '../components/common/Table';

interface ProvincePopulation {
  province: string;
  population: number;
}

const PopulationPage: React.FC = () => {
  const [provincePopulation, setProvincePopulation] = useState<ProvincePopulation[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchPopulationData = async () => {
      try {
        // Fetch provinces
        const provinces = await getProvinces();

        // Ensure provinces is an array
        if (!Array.isArray(provinces)) {
          throw new Error('Provinces data is not an array');
        }

        // Fetch population data for each province
        const populationData = await Promise.all(
          provinces.map(async (province) => {
            const response = await fetch(
              `http://localhost/api/v1/population/data/summary/?province_id=${province.id}`,
              {
                headers: {
                  'Content-Type': 'application/json',
                  Authorization: `Bearer ${localStorage.getItem('jwt_token') || ''}`,
                },
              }
            );

            if (!response.ok) {
              throw new Error(`Failed to fetch population for province ${province.name}: ${response.status}`);
            }

            const data = await response.json();
            if (data.status !== 'success') {
              throw new Error(`API error for province ${province.name}: ${data.message}`);
            }

            return {
              province: province.name,
              population: data.data.total_population || 0,
            };
          })
        );

        setProvincePopulation(populationData);
      } catch (err: any) {
        console.error('Failed to fetch population data:', err);
        setError(err.message || 'Failed to load population data. Please try again later.');
      } finally {
        setLoading(false);
      }
    };

    fetchPopulationData();
  }, []);

  const columns: Column<ProvincePopulation>[] = [
    {
      header: 'Province',
      accessor: 'province',
    },
    {
      header: 'Total Population',
      accessor: 'population',
      render: (value: number) => value.toLocaleString(),
    },
  ];

  return (
    <div className="p-6">
      <h1 className="text-2xl font-bold mb-4">Province Population</h1>
      {loading ? (
        <p>Loading...</p>
      ) : error ? (
        <p className="text-red-500">{error}</p>
      ) : provincePopulation.length === 0 ? (
        <p>No population data available.</p>
      ) : (
        <PaginatedTable columns={columns} data={provincePopulation} />
      )}
    </div>
  );
};

export default PopulationPage;