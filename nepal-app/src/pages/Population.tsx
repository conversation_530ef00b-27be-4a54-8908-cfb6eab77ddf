
import React, { useEffect, useState } from 'react';
import {
  getProvincePopulation,
  getProvinceDistrictsPopulation
} from '../api/services/population.service';

interface PopulationItem {
  id: string;
  name: string;
  population: number;
  wards: number;
  expectedBirths: number;
  expectedPregnancies: number;
  dataYear: number;
  level: 'province' | 'district' | 'municipality' | 'ward';
  parentId?: string;
  children?: PopulationItem[];
  isExpanded?: boolean;
  isLoading?: boolean;
}

const PopulationPage: React.FC = () => {
  const [populationData, setPopulationData] = useState<PopulationItem[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Set the auth token for testing
  useEffect(() => {
    const token = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.XLvW9i6a6ZBdDqSYjx18iOVoX1AtYI89Ilh4F6YrVNE';
    localStorage.setItem('token', token);
    console.log('✅ Test auth token set for population API testing');
  }, []);

  // Load initial province data
  useEffect(() => {
    const fetchProvinceData = async () => {
      try {
        setLoading(true);
        setError(null);

        // Hardcoded province list
        const provinces = [
          { id: 1, name: 'Koshi' },
          { id: 2, name: 'Madhesh' },
          { id: 3, name: 'Bagmati' },
          { id: 4, name: 'Gandaki' },
          { id: 5, name: 'Lumbini' },
          { id: 6, name: 'Karnali' },
          { id: 7, name: 'Sudurpaschim' },
        ];

        console.log('Fetching province population data...');

        const provinceData = await Promise.all(
          provinces.map(async (province) => {
            try {
              const response = await getProvincePopulation(province.id);
              const data = response.data;
              return {
                id: province.id.toString(),
                name: province.name,
                population: data.total_population || 0,
                wards: data.total_wards || 0,
                expectedBirths: data.total_expected_births || 0,
                expectedPregnancies: data.total_expected_pregnancies || 0,
                dataYear: data.data_year || 2024,
                level: 'province' as const,
                children: [],
                isExpanded: false,
                isLoading: false,
              };
            } catch (error) {
              console.error(`Error fetching data for province ${province.name}:`, error);
              return {
                id: province.id.toString(),
                name: province.name,
                population: 0,
                wards: 0,
                expectedBirths: 0,
                expectedPregnancies: 0,
                dataYear: 2024,
                level: 'province' as const,
                children: [],
                isExpanded: false,
                isLoading: false,
              };
            }
          })
        );

        console.log('Province data loaded:', provinceData);
        setPopulationData(provinceData);
      } catch (error) {
        console.error('Error fetching population data:', error);
        setError('Failed to fetch population data. Please try again later.');
      } finally {
        setLoading(false);
      }
    };

    fetchProvinceData();
  }, []);

  // Handle expanding/collapsing items and loading child data
  const toggleExpand = async (item: PopulationItem) => {
    console.log(`Toggling expand for ${item.level}: ${item.name} (ID: ${item.id})`);

    if (item.isExpanded) {
      // Collapse - just toggle the flag
      console.log(`Collapsing ${item.level}: ${item.name}`);
      setPopulationData(prev =>
        updateItemInTree(prev, item.id, { isExpanded: false })
      );
      return;
    }

    // Expand - load child data if not already loaded
    if (item.children && item.children.length > 0) {
      // Already has children, just expand
      console.log(`Expanding ${item.level}: ${item.name} (already has ${item.children.length} children)`);
      setPopulationData(prev =>
        updateItemInTree(prev, item.id, { isExpanded: true })
      );
      return;
    }

    // Need to load children
    console.log(`Loading children for ${item.level}: ${item.name}`);
    try {
      setPopulationData(prev =>
        updateItemInTree(prev, item.id, { isLoading: true })
      );

      let children: PopulationItem[] = [];

      if (item.level === 'province') {
        // Load districts for this province
        console.log(`Loading districts for province ID: ${item.id}`);
        const response = await getProvinceDistrictsPopulation(parseInt(item.id));
        console.log(`Districts response:`, response);
        children = response.data.map((district: any) => ({
          id: district.location_id,
          name: district.location_name,
          population: district.total_population || 0,
          wards: district.total_wards || 0,
          expectedBirths: district.total_expected_births || 0,
          expectedPregnancies: district.total_expected_pregnancies || 0,
          dataYear: district.data_year || 2024,
          level: 'district' as const,
          parentId: item.id,
          children: [],
          isExpanded: false,
          isLoading: false,
        }));
      } else if (item.level === 'district') {
        // Load municipalities for this district by getting all municipalities in the province and filtering
        console.log(`Loading municipalities for district ID: ${item.id}`);

        // Find the province ID for this district
        const provinceId = item.parentId; // District's parent is the province
        console.log(`Getting municipalities for province ${provinceId} and filtering for district ${item.id}`);

        try {
          // Get all municipalities in the province
          const response = await fetch(`${import.meta.env.VITE_API_BASE_URL}/population/data/hierarchy/?province_id=${provinceId}&expand_level=municipality`, {
            headers: {
              'Authorization': `Bearer ${localStorage.getItem('token')}`,
              'Content-Type': 'application/json',
            },
          });
          const municipalityData = await response.json();
          console.log(`All municipalities in province ${provinceId}:`, municipalityData);

          if (municipalityData.status === 'success' && municipalityData.data && municipalityData.data.length > 0) {
            // Filter municipalities that belong to this district
            const districtMunicipalities = municipalityData.data.filter((municipality: any) =>
              municipality.parent_district?.id === item.id
            );

            console.log(`Found ${districtMunicipalities.length} municipalities for district ${item.id}`);

            if (districtMunicipalities.length > 0) {
              children = districtMunicipalities.map((municipality: any) => ({
                id: municipality.location_id,
                name: municipality.location_name,
                population: municipality.total_population || 0,
                wards: municipality.total_wards || 0,
                expectedBirths: municipality.total_expected_births || 0,
                expectedPregnancies: municipality.total_expected_pregnancies || 0,
                dataYear: municipality.data_year || 2024,
                level: 'municipality' as const,
                parentId: item.id,
                children: [],
                isExpanded: false,
                isLoading: false,
              }));
            }
          }
        } catch (error) {
          console.error(`Error fetching municipalities for district ${item.id}:`, error);
        }
      } else if (item.level === 'municipality') {
        // Load wards for this municipality
        console.log(`Loading wards for municipality ID: ${item.id}`);

        try {
          // Use direct API call since the municipality ID might be in string format
          const response = await fetch(`${import.meta.env.VITE_API_BASE_URL}/population/data/hierarchy/?municipality_id=${item.id}&expand_level=ward`, {
            headers: {
              'Authorization': `Bearer ${localStorage.getItem('token')}`,
              'Content-Type': 'application/json',
            },
          });
          const wardData = await response.json();
          console.log(`Wards response for municipality ${item.id}:`, wardData);

          if (wardData.status === 'success' && wardData.data && wardData.data.length > 0) {
            children = wardData.data.map((ward: any) => ({
              id: ward.location_id,
              name: ward.location_name,
              population: ward.total_population || 0,
              wards: ward.total_wards || 0,
              expectedBirths: ward.total_expected_births || 0,
              expectedPregnancies: ward.total_expected_pregnancies || 0,
              dataYear: ward.data_year || 2024,
              level: 'ward' as const,
              parentId: item.id,
              children: [],
              isExpanded: false,
              isLoading: false,
            }));
          } else {
            console.log(`No wards found for municipality ${item.id}`);
          }
        } catch (error) {
          console.error(`Error fetching wards for municipality ${item.id}:`, error);
        }
      }

      console.log(`Loaded ${children.length} children for ${item.level}: ${item.name}`);

      if (children.length === 0) {
        // No children found - show a message
        console.log(`No ${item.level === 'province' ? 'districts' : item.level === 'district' ? 'municipalities' : 'wards'} found for ${item.level}: ${item.name}`);

        // Create a placeholder child to show "No data available"
        children = [{
          id: `${item.id}-no-data`,
          name: `No ${item.level === 'province' ? 'districts' : item.level === 'district' ? 'municipalities' : 'wards'} available`,
          population: 0,
          wards: 0,
          expectedBirths: 0,
          expectedPregnancies: 0,
          dataYear: 2024,
          level: 'ward' as const, // Use ward level so it can't be expanded further
          parentId: item.id,
          children: [],
          isExpanded: false,
          isLoading: false,
        }];
      }

      // Update the item with children and expand it
      setPopulationData(prev =>
        updateItemInTree(prev, item.id, {
          children,
          isExpanded: true,
          isLoading: false
        })
      );

    } catch (error) {
      console.error(`Error loading children for ${item.level} ${item.name}:`, error);
      setPopulationData(prev =>
        updateItemInTree(prev, item.id, { isLoading: false })
      );
    }
  };

  // Helper function to update an item in the tree structure
  const updateItemInTree = (
    items: PopulationItem[],
    targetId: string,
    updates: Partial<PopulationItem>
  ): PopulationItem[] => {
    return items.map(item => {
      if (item.id === targetId) {
        return { ...item, ...updates };
      }
      if (item.children && item.children.length > 0) {
        return {
          ...item,
          children: updateItemInTree(item.children, targetId, updates)
        };
      }
      return item;
    });
  };

  // Render a single population item with its children
  const renderPopulationItem = (item: PopulationItem, depth: number = 0): React.ReactNode => {
    const hasChildren = item.level !== 'ward';
    const canExpand = hasChildren;

    const handleClick = (e: React.MouseEvent) => {
      e.stopPropagation();
      if (hasChildren) {
        toggleExpand(item);
      }
    };

    return (
      <React.Fragment key={item.id}>
        <div
          className={`flex items-center py-3 px-4 hover:bg-gray-50 ${hasChildren ? 'cursor-pointer' : 'cursor-default'}`}
          style={{ paddingLeft: `${depth * 24 + 16}px` }}
          onClick={handleClick}
        >
          {/* Expand/Collapse Icon */}
          <div className="w-6 h-6 mr-3 flex items-center justify-center">
            {hasChildren && (
              item.isLoading ? (
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600"></div>
              ) : canExpand ? (
                item.isExpanded ? (
                  <svg className="w-4 h-4 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                  </svg>
                ) : (
                  <svg className="w-4 h-4 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                  </svg>
                )
              ) : null
            )}
          </div>

          {/* Content */}
          <div className="flex-1 grid grid-cols-6 gap-4 text-sm">
            <div className={`font-medium ${item.id.includes('no-data') ? 'text-gray-500 italic' : 'text-gray-900'}`}>
              {!item.id.includes('no-data') && (
                <span className={`inline-block px-2 py-1 rounded text-xs font-medium mr-2 ${
                  item.level === 'province' ? 'bg-blue-100 text-blue-800' :
                  item.level === 'district' ? 'bg-green-100 text-green-800' :
                  item.level === 'municipality' ? 'bg-yellow-100 text-yellow-800' :
                  'bg-gray-100 text-gray-800'
                }`}>
                  {item.level.charAt(0).toUpperCase() + item.level.slice(1)}
                </span>
              )}
              {item.name}
            </div>
            <div className={`${item.id.includes('no-data') ? 'text-gray-400' : 'text-gray-600'}`}>
              {item.id.includes('no-data') ? '-' : item.population.toLocaleString()}
            </div>
            <div className={`${item.id.includes('no-data') ? 'text-gray-400' : 'text-gray-600'}`}>
              {item.id.includes('no-data') ? '-' : item.wards.toLocaleString()}
            </div>
            <div className={`${item.id.includes('no-data') ? 'text-gray-400' : 'text-gray-600'}`}>
              {item.id.includes('no-data') ? '-' : item.expectedBirths.toLocaleString()}
            </div>
            <div className={`${item.id.includes('no-data') ? 'text-gray-400' : 'text-gray-600'}`}>
              {item.id.includes('no-data') ? '-' : item.expectedPregnancies.toLocaleString()}
            </div>
            <div className={`${item.id.includes('no-data') ? 'text-gray-400' : 'text-gray-600'}`}>
              {item.id.includes('no-data') ? '-' : item.dataYear}
            </div>
          </div>
        </div>

        {/* Render children if expanded */}
        {item.isExpanded && item.children && item.children.map(child =>
          renderPopulationItem(child, depth + 1)
        )}
      </React.Fragment>
    );
  };

  return (
    <div className="p-6">
      <div className="mb-6">
        <div className="flex justify-between items-start mb-4">
          <div>
            <h1 className="text-3xl font-bold text-gray-900 mb-2">Hierarchical Population Data</h1>
            <p className="text-gray-600">
              Explore population data by expanding provinces → districts → municipalities → wards
            </p>
          </div>
          <button
            onClick={() => window.location.reload()}
            className="px-3 py-1 bg-gray-500 text-white rounded-md text-sm font-medium hover:bg-gray-600 transition-colors"
          >
            Refresh
          </button>
        </div>
      </div>

      {loading ? (
        <div className="flex items-center justify-center py-12">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
          <span className="ml-3 text-gray-600">Loading population data...</span>
        </div>
      ) : error ? (
        <div className="bg-red-50 border border-red-200 rounded-md p-4">
          <div className="flex">
            <div className="flex-shrink-0">
              <svg className="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
              </svg>
            </div>
            <div className="ml-3">
              <h3 className="text-sm font-medium text-red-800">Error loading data</h3>
              <p className="text-sm text-red-700 mt-1">{error}</p>
            </div>
          </div>
        </div>
      ) : populationData.length === 0 ? (
        <div className="text-center py-12">
          <svg className="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
          </svg>
          <h3 className="mt-2 text-sm font-medium text-gray-900">No population data available</h3>
          <p className="mt-1 text-sm text-gray-500">No population data could be found for any provinces.</p>
        </div>
      ) : (
        <div className="bg-white shadow rounded-lg">
          {/* Header */}
          <div className="px-4 py-3 border-b border-gray-200 bg-gray-50">
            <div className="grid grid-cols-6 gap-4 text-sm font-medium text-gray-700">
              <div className="pl-10">Location</div>
              <div>Population</div>
              <div>Wards</div>
              <div>Expected Births</div>
              <div>Expected Pregnancies</div>
              <div>Data Year</div>
            </div>
          </div>

          {/* Summary */}
          <div className="px-4 py-3 border-b border-gray-200 bg-blue-50">
            <div className="flex flex-wrap gap-4 text-sm text-blue-700">
              <span className="font-medium">
                Total Population: {populationData.reduce((sum, p) => sum + p.population, 0).toLocaleString()}
              </span>
              <span className="font-medium">
                Total Wards: {populationData.reduce((sum, p) => sum + p.wards, 0).toLocaleString()}
              </span>
              <span className="font-medium">
                Provinces: {populationData.length}
              </span>
            </div>
          </div>

          {/* Hierarchical Data */}
          <div className="max-h-96 overflow-y-auto">
            {populationData.map(item => renderPopulationItem(item))}
          </div>
        </div>
      )}
    </div>
  );
};

export default PopulationPage;