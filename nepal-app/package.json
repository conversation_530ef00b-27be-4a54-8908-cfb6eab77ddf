{"name": "nepal-app", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@headlessui/react": "^2.2.4", "@tailwindcss/vite": "^4.1.7", "@turf/boolean-point-in-polygon": "^7.2.0", "@turf/helpers": "^7.2.0", "axios": "^1.9.0", "clsx": "^2.1.1", "focus-trap-react": "^11.0.4", "highcharts": "^12.2.0", "highcharts-react-official": "^3.2.2", "i18next-http-backend": "^3.0.2", "jwt-decode": "^4.0.0", "leaflet": "^1.9.4", "lucide-react": "^0.511.0", "react": "^19.1.0", "react-dom": "^19.1.0", "react-i18next": "^15.5.2", "react-leaflet": "^5.0.0", "react-router-dom": "^7.6.1", "react-select": "^5.10.1", "tailwindcss": "^4.1.7"}, "devDependencies": {"@eslint/js": "^9.25.0", "@types/highcharts": "^7.0.0", "@types/leaflet": "^1.9.18", "@types/react": "^19.1.6", "@types/react-dom": "^19.1.2", "@types/react-leaflet": "^2.8.3", "@types/react-router-dom": "^5.3.3", "@types/react-select": "^5.0.1", "@vitejs/plugin-react": "^4.4.1", "eslint": "^9.25.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^16.0.0", "typescript": "~5.8.3", "typescript-eslint": "^8.30.1", "vite": "^6.3.5", "vite-plugin-svgr": "^4.3.0"}}